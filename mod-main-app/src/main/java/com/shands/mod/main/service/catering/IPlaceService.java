package com.shands.mod.main.service.catering;

import com.shands.mod.dao.model.catering.HotelCodeTransferReq;
import com.shands.mod.dao.model.catering.HotelLabelInfoRes;
import com.shands.mod.dao.model.catering.HotelPlaceInfoReq;
import com.shands.mod.dao.model.catering.HotelPlaceInfoRes;
import com.shands.mod.dao.model.catering.HotelTableInfoReq;
import com.shands.mod.dao.model.catering.HotelTableInfoRes;
import com.shands.mod.dao.model.catering.LvYunPageVO;
import com.shands.mod.dao.model.catering.TableQrCodeReq;
import com.shands.mod.dao.model.catering.TableQrCodeRes;
import com.shands.mod.vo.ResultVO;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/17 14:51
 */
public interface IPlaceService {


  /**
   * 通过酒店code获取酒店标签信息
   *
   * @return 酒店标签信息
   */
  List<HotelLabelInfoRes> getHotelLabelInfo(HotelCodeTransferReq req);

  /**
   * 根据companyId获取绿云hotelId
   * @return 绿云hotelId
   */
  Integer getLvYunHotelId(Integer companyId);


  /**
   * @param req 绿云hotelId，场所名（支持前置模糊查询）
   * @return 酒店场所信息列表
   */
  ResultVO<List<HotelPlaceInfoRes>> listCateringPlace(HotelPlaceInfoReq req);


  /**
   * @param req 绿云hotelId,场所code
   * @return 酒店当前场所下的桌号列表
   */
  ResultVO<LvYunPageVO<HotelTableInfoRes>> listCateringTable(HotelTableInfoReq req);


  /**
   * 获取桌号二维码
   * @param req 场所code,桌号code
   * @return 桌号二维码
   */
  ResultVO<TableQrCodeRes> getTableQrCode(TableQrCodeReq req);


}
