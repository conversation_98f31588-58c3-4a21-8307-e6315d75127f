package com.shands.mod.main.config;


import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@RefreshScope
@Data
@Component
public class BetterwoodConfig {

  @Value("${betterwood-config.betterwoodPrivateKey}")
  private String betterwoodPrivateKey;

  @Value("${betterwood-config.betterwoodApiUrl}")
  private String betterwoodApiUrl;

  @Value("${betterwood-config.betterwoodPlatformUrl}")
  private String betterwoodPlatformUrl;

  @Value("${betterwood-config.sellServiceApiUrl:}")
  private String betterwoodSellServiceApiUrl;

  @Value("${betterwood-config.sellBdwServiceApiUrl:}")
  private String betterwoodSellBdwServiceApiUrl;
  @Value("${betterwood-config.hotelDetailPriceUrl:}")
  private String betterwoodHotelDetailPriceUrl;

  /**
   * 百达屋小程序-行程卡片地址
   */
  @Value("${betterwood-config.betterwoodJourneyCardPage:modules/hotel/views/order-detail/OrderDetail}")
  private String betterwoodJourneyCardPage;


  /**
   * 百达屋小程序-卡片地址
   */
  @Value("${betterwood-config.betterwoodMiniCodePage:modules/hotel/views/trip-order/index}")
  private String betterwoodMiniCodePage;

  /**
   * 百达屋小程序首页-卡片地址
   */
  @Value("${betterwood-config.betterwoodMiniCodeIndexPage:modules/main/views/bookHotel/BookHotel}")
  private String betterwoodMiniCodeIndexPage;


  /**
   * 查看百达屋主单信息
   */
  @Value("${betterwood-config.getOrderMainInfoUrl:}")
  private String getOrderInfoUrl;

  @Value("${betterwood-config.createOrderUrl:}")
  private String createOrderUrl;

  @Value("${betterwood-config.eventType.orderShare:129}")
  private String eventTypeOrderShare;

  @Value("${betterwood-config.betterwoodOrderScanAppId}")
  private String betterwoodOrderScanAppId;

  @Value("${betterwood-config.betterwoodOrderScan}")
  private String betterwoodOrderScan;

  @Value("${betterwood-config.cancelOrderUrl:}")
  private String cancelOrderUrl;
  /**
   * 小程序二维码分享标题
   */
  @Value("${betterwood-config.memberCodeShareTitle:}")
  private String betterwoodShareMemberCodeTitle;
  /**
   * 小程序二维码分享图片
   */
  @Value("${betterwood-config.memberCodeShareImageUrl:}")
  private String betterwoodShareMemberCodeImageUrl;

  @Value("${betterwood-config.queryUserIdentityTagUrl:}")
  private String queryUserIdentityTagUrl;

  @Value("${betterwood-config.queryVoucherTemplateInfoUrl:}")
  private String queryVoucherTemplateInfoUrl;

  @Value("${betterwood-config.querySendRecordDetailPageUrl:}")
  private String querySendRecordDetailPageUrl;

  @Value("${betterwood-config.sendHotelVoucherUrl:}")
  private String sendHotelVoucherUrl;

  @Value("${betterwood-config.batchSendHotelVoucherUrl:}")
  private String batchSendHotelVoucherUrl;

  @Value("${betterwood-config.templatePreCreateUrl:}")
  private String templatePreCreateUrl;



  @Value("${betterwood-config.queryAvaServiceApiUrl:}")
  private String queryAvaServiceApiUrl;

  /**
   * 百达屋-领券页面
   */
  @Value("${betterwood-config.h5VoucherPage:https://h5.betterwood.com/#/send-voucher?code=%s&formType=WECHAT}")
  private String h5VoucherPage;



  /**
   * 百达卡H5地址
   */
  @Value("${betterwood-config.scanMebCardH5Path:https://h5.betterwood.com/minprogram?t=h5&h5Path=catering-card&app=%s&min=%s}")
  private String scanMebCardH5Path;

  /**
   * 扫码住h5
   */
  @Value("${betterwood-config.scanLiveH5Path:https://h5.betterwood.com/minprogram?t=ckI&app=%s&min=%s}")
  private String scanLiveH5Path;

  /**
   * 业绩二级页面H5
   */
  @Value("${betterwood-config.sxePerformanceH5Path:https://mod-campaign-h5.betterwood.com/sxe/performance?tab=%s&type=%s}")
  private String sxePerformanceH5Path;

  @Value("${betterwood-config.queryDate:}")
  private String queryDate;
}
