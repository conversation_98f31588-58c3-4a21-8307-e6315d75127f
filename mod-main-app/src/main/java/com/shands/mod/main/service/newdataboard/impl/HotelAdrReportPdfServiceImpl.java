package com.shands.mod.main.service.newdataboard.impl;

import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.itextpdf.layout.properties.VerticalAlignment;
import com.shands.mod.dao.model.datarevision.vo.AdrReportRowVo;
import com.shands.mod.main.service.newdataboard.HotelAdrReportPdfService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * 酒店ADR报表PDF生成服务
 * <AUTHOR>
 * @date 2025-01-01
 */
@Slf4j
@Service
public class HotelAdrReportPdfServiceImpl implements HotelAdrReportPdfService {




    @Override
    public void generateHotelAdrReportPdf(String hotelName, String yearMonth,
                                          List<AdrReportRowVo> reportData, String outputPath) {
        try {
            // 创建PDF写入器和文档
            PdfWriter writer = new PdfWriter(new FileOutputStream(outputPath));
            PdfDocument pdfDocument = new PdfDocument(writer);
            Document document = new Document(pdfDocument, PageSize.A4.rotate()); // 横向页面

            // 设置中文字体
            PdfFont chineseFont = PdfFontFactory.createFont("STSong-Light", "UniGB-UCS2-H");
            
            // 创建表格 - 8列
            float[] columnWidths = {1.5f, 1.5f, 1.2f, 1f, 1f, 1.5f, 1f, 1f};
            Table table = new Table(UnitValue.createPercentArray(columnWidths));
            table.setWidth(UnitValue.createPercentValue(100));

            // 添加标题和副标题到表格
            addTitleRows(table, hotelName, yearMonth, chineseFont);

            // 添加表头
            addTableHeaders(table, chineseFont);

            // 添加数据行
            addTableData(table, reportData, chineseFont);

            document.add(table);
            document.close();
            
            log.info("PDF报表生成成功: {}", outputPath);
            
        } catch (IOException e) {
            log.error("生成PDF报表失败", e);
            throw new RuntimeException("生成PDF报表失败", e);
        }
    }

    @Override
    public void addHotelAdrReportToDocument(PdfDocument pdfDocument, String hotelName, String yearMonth,
                                           List<AdrReportRowVo> reportData) {
        try {
            // 添加新页面
            pdfDocument.addNewPage(PageSize.A4.rotate());

            // 获取最后一页
            int pageNumber = pdfDocument.getNumberOfPages();

            // 创建文档对象，只针对新页面，避免与现有页面重合
            Document document = new Document(pdfDocument, PageSize.A4.rotate(), false);

            // 设置中文字体 - 需要使用配置文件中的字体路径
            PdfFont chineseFont = PdfFontFactory.createFont("STSong-Light", "UniGB-UCS2-H");

            // 添加独立的标题和副标题
            addIndependentTitles(document, hotelName, yearMonth, chineseFont);

            // 创建表格 - 8列（不包含标题和副标题）
            float[] columnWidths = {1.5f, 1.5f, 1.2f, 1f, 1f, 1.5f, 1f, 1f};
            Table table = new Table(UnitValue.createPercentArray(columnWidths));
            table.setWidth(UnitValue.createPercentValue(100));

            // 设置表格上边距为0，与副标题紧密连接
            table.setMarginTop(0);

            // 添加表头（作为普通行，不会在分页时重复）
            addTableHeadersAsRegularRows(table, chineseFont);

            // 添加数据行
            addTableData(table, reportData, chineseFont);

            // 将表格添加到文档
            document.add(table);

            // 关闭文档对象，但不关闭PdfDocument
            document.close();

            log.info("渠道房型收益报表已添加到PDF文档的第{}页", pageNumber);

        } catch (IOException e) {
            log.error("添加渠道房型收益报表到PDF文档失败", e);
            throw new RuntimeException("添加渠道房型收益报表到PDF文档失败", e);
        }
    }

    /**
     * 添加独立的标题和副标题
     */
    private void addIndependentTitles(Document document, String hotelName, String yearMonth, PdfFont font) {
        // 添加主标题
        Paragraph mainTitle = new Paragraph(hotelName)
                .setFont(font)
                .setFontSize(18)
                .setBold()
                .setTextAlignment(TextAlignment.CENTER)
                .setFontColor(ColorConstants.WHITE)
                .setBackgroundColor(new DeviceRgb(19, 60, 154))
                .setPadding(15)
                .setCharacterSpacing(1)
                .setMarginBottom(0);
        
        document.add(mainTitle);
        
        // 添加副标题 - 设置下边距为0，与表头紧密连接
        Paragraph subTitle = new Paragraph("渠道房型实收 ADR " + yearMonth)
                .setFont(font)
                .setFontSize(12)
                .setBold()
                .setTextAlignment(TextAlignment.CENTER)
                .setFontColor(ColorConstants.WHITE)
                .setBackgroundColor(new DeviceRgb(19, 60, 154))
                .setPadding(7.5f)
                .setCharacterSpacing(1)
                .setMarginTop(0)
                .setMarginBottom(0); // 设置下边距为0
        
        document.add(subTitle);
    }

    /**
     * 添加标题行和副标题行到表格
     */
    private void addTitleRows(Table table, String hotelName, String yearMonth, PdfFont font) {
        // 添加主标题行 - 跨越所有8列，占2行高度
        Cell titleCell = new Cell(2, 8); // 2行高度，8列宽度
        titleCell.add(new Paragraph(hotelName).setFont(font).setFontSize(18).setBold());
        titleCell.setTextAlignment(TextAlignment.CENTER);
        titleCell.setVerticalAlignment(VerticalAlignment.MIDDLE);
        titleCell.setBackgroundColor(new DeviceRgb(19, 60,154));
        titleCell.setFontColor(ColorConstants.WHITE);
        titleCell.setPadding(15);
        titleCell.setBold();
        titleCell.setCharacterSpacing(1);
        table.addHeaderCell(titleCell);

        // 添加副标题行 - 跨越所有8列，占1行高度
        Cell subtitleCell = new Cell(1, 8); // 1行高度，8列宽度
        subtitleCell.add(new Paragraph("渠道房型实收 ADR " + yearMonth).setFont(font).setFontSize(12).setBold());
        subtitleCell.setTextAlignment(TextAlignment.CENTER);
        subtitleCell.setVerticalAlignment(VerticalAlignment.MIDDLE);
        subtitleCell.setBackgroundColor(new DeviceRgb(19, 60,154));
        subtitleCell.setFontColor(ColorConstants.WHITE);
        subtitleCell.setPadding(7.5f);
        subtitleCell.setBold();
        subtitleCell.setCharacterSpacing(1);
        table.addHeaderCell(subtitleCell);
    }

    /**
     * 添加表格头部
     */
    private void addTableHeaders(Table table, PdfFont font) {
        // 第一行表头
        table.addHeaderCell(createHeaderCell("房型&渠道", font, 2, 1)); // 第一列加粗
        table.addHeaderCell(createHeaderCell("综合门市价", font, 2, 1));
        table.addHeaderCell(createHeaderCell("已售间夜", font, 2, 1));
        table.addHeaderCell(createHeaderCell("合佣金", font, 1, 2));
        table.addHeaderCell(createHeaderCell("实际佣金率", font, 2, 1));
        table.addHeaderCell(createHeaderCell("实收", font, 1, 2));

        // 第二行表头
        table.addHeaderCell(createHeaderCell("房费", font, 1, 1));
        table.addHeaderCell(createHeaderCell("ADR", font, 1, 1));
        table.addHeaderCell(createHeaderCell("房费", font, 1, 1));
        table.addHeaderCell(createHeaderCell("ADR", font, 1, 1)); // 最后一列加粗
    }

    /**
     * 添加表格头部作为普通行（不会在分页时重复）
     */
    private void addTableHeadersAsRegularRows(Table table, PdfFont font) {
        // 第一行表头
        table.addCell(createHeaderCellAsRegular("房型&渠道", font, 2, 1)); // 第一列加粗
        table.addCell(createHeaderCellAsRegular("综合门市价", font, 2, 1));
        table.addCell(createHeaderCellAsRegular("已售间夜", font, 2, 1));
        table.addCell(createHeaderCellAsRegular("含佣金", font, 1, 2));
        table.addCell(createHeaderCellAsRegular("实际佣金率", font, 2, 1));
        table.addCell(createHeaderCellAsRegular("实收", font, 1, 2));

        // 第二行表头
        table.addCell(createHeaderCellAsRegular("房费", font, 1, 1));
        table.addCell(createHeaderCellAsRegular("ADR", font, 1, 1));
        table.addCell(createHeaderCellAsRegular("房费", font, 1, 1));
        table.addCell(createHeaderCellAsRegular("ADR", font, 1, 1)); // 最后一列加粗
    }

    /**
     * 创建表头单元格
     */
    private Cell createHeaderCell(String content, PdfFont font, int rowspan, int colspan) {
        Cell cell = new Cell(rowspan, colspan);
        Paragraph paragraph = new Paragraph(content).setFont(font).setFontSize(10);
        
        // 加粗设置样式
        paragraph.setBold();

        cell.add(paragraph);
        cell.setTextAlignment(TextAlignment.CENTER); // 水平居中
        cell.setVerticalAlignment(VerticalAlignment.MIDDLE); // 垂直居中
        cell.setPadding(1); // 增加表头内边距，让表头更舒适
        return cell;
    }

    /**
     * 创建表头单元格作为普通行
     */
    private Cell createHeaderCellAsRegular(String content, PdfFont font, int rowspan, int colspan) {
        Cell cell = new Cell(rowspan, colspan);
        Paragraph paragraph = new Paragraph(content).setFont(font).setFontSize(10);
        
        // 加粗设置样式
        paragraph.setBold();

        cell.add(paragraph);
        cell.setTextAlignment(TextAlignment.CENTER); // 水平居中
        cell.setVerticalAlignment(VerticalAlignment.MIDDLE); // 垂直居中
        cell.setPadding(1); // 增加表头内边距，让表头更舒适
        return cell;
    }

    /**
     * 添加表格数据
     */
    private void addTableData(Table table, List<AdrReportRowVo> reportData, PdfFont font) {
        for (AdrReportRowVo row : reportData) {

            // 房型&渠道（第一列，加粗）
            table.addCell(createDataCell(row, row.getRoomTypeChannel(), font, true, true, false));

            // 其他中间列（普通样式）
            table.addCell(createDataCell(row, row.getMarketPrice(), font, false, false,false));
            table.addCell(createDataCell(row, row.getSoldRooms(), font, false, false, false));
            table.addCell(createDataCell(row, row.getCommissionRoomFee(), font, false, false, false));
            table.addCell(createDataCell(row, row.getCommissionAdr(), font, false, false, false));
            table.addCell(createDataCell(row, row.getActualCommissionRate(), font, false, false, false));
            table.addCell(createDataCell(row, row.getActualRoomFee(), font,  false, false, false));
            
            // 最后一列（加粗）
            table.addCell(createDataCell(row, row.getActualAdr(), font, true, false, true));
        }
    }

    /**
     * 创建数据单元格
     */
    private Cell createDataCell(AdrReportRowVo row, String content, PdfFont font, boolean isBold, boolean firstCol, boolean lastCol) {

        Color backgroundColor = row.isHighlighted() ? ColorConstants.YELLOW : lastCol ? ColorConstants.LIGHT_GRAY : null;
        Color fontColor = row.isMaxAdrDifference() ? ColorConstants.RED : ColorConstants.BLACK;
        TextAlignment alignment = firstCol ?
                row.isSubItem() ?  TextAlignment.RIGHT : TextAlignment.LEFT
                : TextAlignment.CENTER;


        Cell cell = new Cell();
        Paragraph paragraph = new Paragraph(content != null ? content : "")
                .setFont(font)
                .setFontSize(9)
                .setFontColor(fontColor);
        
        // 添加字符间距
        paragraph.setCharacterSpacing(1f); // 增加字符间距

        // 根据是否需要加粗设置样式
        if (isBold) {
            paragraph.setBold();
        }

        cell.setTextAlignment(alignment);

        cell.add(paragraph);
        cell.setVerticalAlignment(VerticalAlignment.MIDDLE);
        cell.setPadding(1); // 增加单元格内边距
        if (Objects.nonNull(backgroundColor)) {
            cell.setBackgroundColor(backgroundColor);
        }
        return cell;
    }

    /**
     * 生成示例数据并创建PDF
     */
    public void generateSampleReport(String outputPath) {
        String hotelName = "深圳蛇口海上世界开元名庭酒店";
        String yearMonth = "2025年4月";
        
        List<AdrReportRowVo> sampleData = List.of(
                new AdrReportRowVo("名成豪华双床房", "486", "115", "53,384", "464", "", "51,225", "445", true, false, false),
                new AdrReportRowVo("百达屋", "566", "2", "988", "494", "", "947", "473", false, true, true),
                new AdrReportRowVo("会员价", "566", "2", "988", "494", "", "947", "473", false, true, false),
                new AdrReportRowVo("百达卡", "558", "14", "6,150", "439", "", "5,867", "419", false, true, false),
                new AdrReportRowVo("OTA", "566", "2", "988", "494", "", "947", "473", false, false, false),
                new AdrReportRowVo("携程", "593", "21", "11,166", "532", "", "8,786", "418", false, true, false),
                new AdrReportRowVo("美团", "593", "10", "5,166", "517", "", "4,546", "455", false, true, false),
                new AdrReportRowVo("飞猪", "575", "5", "2,425", "485", "", "2,183", "437", false, true, false),
                new AdrReportRowVo("抖音", "575", "5", "2,425", "485", "", "2,183", "437", false, true, true),
                new AdrReportRowVo("协议客户", "566", "2", "988", "494", "", "947", "473", false, false, false),
                new AdrReportRowVo("企业卡", "429", "69", "30,703", "445", "", "30,346", "440", false, true, false),
                new AdrReportRowVo("线下", "680", "28", "19,026", "679", "", "18,951", "677", false, true, false),
                new AdrReportRowVo("名成豪华大床房", "480", "235", "106,617", "454", "", "100,513", "428", true, false, false)
        );
        
        generateHotelAdrReportPdf(hotelName, yearMonth, sampleData, outputPath);
    }

} 