package com.shands.mod.main.service.newdataboard.impl;

import cn.hutool.core.util.StrUtil;
import com.delonix.bi.dao.mapper.AdsFinaDlEcoAppOwnerCoreDMapper;
import com.delonix.bi.dao.mapper.AdsFinaDlEcoAppOwnerCoreHMapper;
import com.delonix.bi.dao.mapper.AdsFinaDlEcoAppSettlementBillMMapper;
import com.delonix.bi.dao.mapper.AdsTradeHotelChannelAdrFeeMMapper;
import com.delonix.bi.dao.model.*;
import com.delonix.bi.dao.model.dto.AdsFinaDlEcoAppSettlementBillMDto;
import com.itextpdf.forms.PdfAcroForm;
import com.itextpdf.forms.fields.PdfFormField;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.extgstate.PdfExtGState;
import com.itextpdf.layout.Canvas;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.VerticalAlignment;
import com.shands.mod.dao.mapper.SettleBillFileRecordsMapper;
import com.shands.mod.dao.mapper.board.ModNewDataBoardMapper;
import com.shands.mod.dao.mapper.humanresources.ModEmailRecordMapper;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.model.SettleBillFileRecords;
import com.shands.mod.dao.model.SettleBillFileRecordsExample;
import com.shands.mod.dao.model.SettleBillFileRecordsExample.Criteria;
import com.shands.mod.dao.model.datarevision.po.AdsDataBoardTodayPo;
import com.shands.mod.dao.model.datarevision.po.AdsFinaDlEcoAppOwnerCoreDPo;
import com.shands.mod.dao.model.datarevision.vo.*;
import com.shands.mod.dao.model.datarevision.vo.AdrReportRowVo;
import com.shands.mod.dao.model.enums.HotelTypeEnum;
import com.shands.mod.dao.model.enums.UserRightsTypeEnum;
import com.shands.mod.dao.model.enums.board.BizIncomeTrendEnum;
import com.shands.mod.dao.model.enums.board.BizSoldTrendEnum;
import com.shands.mod.dao.model.enums.board.BusinessConditionEnum;
import com.shands.mod.dao.model.enums.board.DataBoardModuleEnum;
import com.shands.mod.dao.model.enums.board.DataTypeEnum;
import com.shands.mod.dao.model.enums.board.FinalStatementEnum;
import com.shands.mod.dao.model.enums.board.TodayRoomSoldEnum;
import com.shands.mod.dao.model.humanresources.ModEmailRecord;
import com.shands.mod.dao.model.newDataBoard.bo.DataBulletinBoardBo;
import com.shands.mod.dao.model.newDataBoard.bo.SettleBillBo;
import com.shands.mod.dao.model.newDataBoard.vo.HomeDataDetailsVo;
import com.shands.mod.dao.model.statistics.vo.BaseStatisticsDataVo;
import com.shands.mod.dao.util.ThousandSeparatorUtil;
import com.shands.mod.exception.NormalException;
import com.shands.mod.exception.ServiceException;
import com.shands.mod.file.BillFileStorageUtil;
import com.shands.mod.file.config.HotelBillConfig;
import com.shands.mod.main.constant.CommonConstant;
import com.shands.mod.main.enums.ChannelTypeEnum;
import com.shands.mod.main.enums.HotelBillPdfEnum;
import com.shands.mod.main.service.common.UserInfoCommonService;
import com.shands.mod.main.service.newdataboard.BossReportService;
import com.shands.mod.main.service.newdataboard.HotelAdrReportPdfService;
import com.shands.mod.main.util.BaiDaWuRSAUtils;
import com.shands.mod.main.util.DateUtil;
import com.shands.mod.main.util.DateUtils;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.main.vo.HotelBillPdfVO;
import com.shands.mod.service.ILockService;
import com.shands.mod.service.ModUserCommonService;
import com.shands.mod.util.BaseConstants;
import com.shands.mod.util.Tools;
import com.shands.mod.vo.UserInfoVO;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: 业主看板数据实现类
 * @Author: wj
 * @Date: 2024/10/21 09:30
 */
@Service
@Slf4j
public class BossReportServiceImpl implements BossReportService {

  @Autowired
  private UserInfoCommonService userInfoCommonService;

  @Autowired
  private ModUserCommonService modUserCommonService;

  @Autowired
  private AdsFinaDlEcoAppOwnerCoreDMapper biCoreDMapper;

  @Autowired
  private AdsFinaDlEcoAppOwnerCoreHMapper biCoreHMapper;

  @Autowired
  private AdsFinaDlEcoAppSettlementBillMMapper biSettlementBillMMapper;

  @Autowired
  private ModNewDataBoardMapper modNewDataBoardMapper;

  @Autowired
  private SettleBillFileRecordsMapper billFileRecordsMapper;

  @Autowired
  private HotelBillConfig hotelBillConfig;

  @Autowired
  private BillFileStorageUtil billFileStorageUtil;

  @Autowired
  private ModHotelInfoDao hotelInfoDao;

  @Autowired
  private ILockService lockService;

  @Autowired
  private AdsTradeHotelChannelAdrFeeMMapper adsTradeHotelChannelAdrFeeMMapper;

  @Autowired
  private HotelAdrReportPdfService hotelAdrReportPdfService;

  @Resource
  private ModEmailRecordMapper modEmailRecordMapper;

  /**
   * 经营情况看板
   *
   * @param req
   * @return
   */
  @Override
  public List<BusinessBoardVo> businessBoard(DataBulletinBoardBo req) throws Exception {
    /**
     * 1. 获取当前用户id
     * 2. 判断是否有菜单权限
     * 3. 判断是否有数据权限
     * 4. 查询bi数据表
     * 5. 组装文案\提示\值
     */
    Integer userId = ThreadLocalHelper.getUser().getId();
    List<String> param = userInfoCommonService.getUserRights(
        userId, ThreadLocalHelper.getCompanyId(),
        UserRightsTypeEnum.APP);
    final String businessConditionCode = DataBoardModuleEnum.BUSINESS_CONDITION.name()
        .toLowerCase();
    if (CollectionUtils.isEmpty(param) || !param.contains(businessConditionCode)) {
      throw new NormalException("用户权限不足,请联系管理员！");
    }

    List<String> userRoles = modUserCommonService.getUserRoles(userId);
    List<ModuleMenuVo> dataBoards = modNewDataBoardMapper.findModuleMenuDetailList(
        DataTypeEnum.HOTEL.name(), userRoles, userId);
    ModuleMenuVo vo = dataBoards.stream()
        .filter(v -> businessConditionCode.equals(v.getModuleCode()))
        .findFirst().orElse(null);
    if (Objects.isNull(vo)) {
      throw new NormalException("用户权限不足,请联系管理员！");
    }

    AdsFinaDlEcoAppOwnerCoreDPo coreDPo = biCoreDMapper.collectByTimeRange(
        req.getHotelCode(), DateUtils.strConverDateTime(req.getStartTime()),
        DateUtils.strConverDateTime(req.getEndTime()));
    if (Objects.isNull(coreDPo)) {
      throw new NormalException("暂无数据");
    }
    List<BusinessBoardVo> result = new ArrayList<>();

    List<ModuleMenuVo> firstLevelList = dataBoards.stream()
        .filter(v -> v.getPId().equals(vo.getId())
            && BusinessConditionEnum.isDashboardItem(v.getModuleCode())
            && (StringUtils.isEmpty(req.getCode()) || v.getModuleCode().equals(req.getCode())))
        .sorted(Comparator.comparing(ModuleMenuVo::getSort))
        .collect(Collectors.toList());

    for (ModuleMenuVo first : firstLevelList) {
      List<HomeDataDetailsVo> voList = new ArrayList<>();
      List<ModuleMenuVo> secondLevelList = dataBoards.stream().filter(
              v -> v.getPId().equals(first.getId()))
          .sorted(Comparator.comparing(ModuleMenuVo::getSort))
          .collect(Collectors.toList());
      for (ModuleMenuVo second : secondLevelList) {
        HomeDataDetailsVo detailsVo = HomeDataDetailsVo.builder()
            .name(second.getModuleName())
            .desc(second.getDescName())
            .code(second.getModuleCode())
            .pCode(first.getModuleCode())
            .sort(second.getSort())
            .unit(second.getUnit())
            .build();
        // 赋值操作
        this.fillValue(coreDPo, detailsVo);
        this.fillNameWithUnit(detailsVo);
        this.fillPercentUnit(detailsVo);

        voList.add(detailsVo);
      }
      BusinessBoardVo boardVo = BusinessBoardVo.builder()
          .name(first.getModuleName())
          .code(first.getModuleCode())
          .sort(first.getSort())
          .desc(first.getDescName())
          .voList(voList)
          .build();
      result.add(boardVo);
    }
    return result;
  }

  private void fillValue(AdsFinaDlEcoAppOwnerCoreDPo coreDPo, HomeDataDetailsVo detailsVo){
    String code = detailsVo.getCode();
    Object value = coreDPo.getValueByFieldName(code);
    if (Objects.nonNull(value)){
      detailsVo.setValue(String.valueOf(value));
      if (value instanceof Double){
        Double doubleValue = (Double) value;
        if (Double.compare(doubleValue, 10_000d) > 0){
          detailsVo.setValue(String.valueOf(doubleValue / 10_000d));
          detailsVo.setUnit("万");
        }
      }
    }
  }

  private static final Set<String> VALID_UNITS = new HashSet<>(Set.of("元", "万"));

  private void fillNameWithUnit(HomeDataDetailsVo detailsVo) {
    if (VALID_UNITS.contains(detailsVo.getUnit())) {
      detailsVo.setName(String.format("%s(%s)", detailsVo.getName(), detailsVo.getUnit()));
    }
  }

  private static final Set<String> PERCENT_UNIT_CODES = new HashSet<>(Set.of("biz_in_trade_occ", "biz_in_completion_rate"));
  private void fillPercentUnit(HomeDataDetailsVo detailsVo) {
    if (PERCENT_UNIT_CODES.contains(detailsVo.getCode())
        && Double.parseDouble(detailsVo.getValue()) > 0d) {
      detailsVo.setUnit(CommonConstant.PERCENT_SYMBOL);
    }
  }

  /**
   * 经营情况报表
   * todo 优化
   * @param req
   * @return
   */
  @Override
  public List<MultiTableListVo> businessList(DataBulletinBoardBo req) throws Exception {
    Integer userId = ThreadLocalHelper.getUser().getId();
    List<String> param = userInfoCommonService.getUserRights(
        userId, ThreadLocalHelper.getCompanyId(),
        UserRightsTypeEnum.APP);
    final String businessConditionCode = DataBoardModuleEnum.BUSINESS_CONDITION.name()
        .toLowerCase();
    if (CollectionUtils.isEmpty(param) || !param.contains(businessConditionCode)) {
      throw new NormalException("用户权限不足,请联系管理员！");
    }

    List<String> userRoles = modUserCommonService.getUserRoles(userId);
    List<ModuleMenuVo> dataBoards = modNewDataBoardMapper.findModuleMenuDetailList(
        DataTypeEnum.HOTEL.name(), userRoles, userId);
    ModuleMenuVo vo = dataBoards.stream()
        .filter(v -> businessConditionCode.equals(v.getModuleCode()))
        .findFirst().orElse(null);
    if (Objects.isNull(vo)) {
      throw new NormalException("用户权限不足,请联系管理员！");
    }
    AdsFinaDlEcoAppOwnerCoreDExample example = new AdsFinaDlEcoAppOwnerCoreDExample();
    example.createCriteria().andHotelCodeEqualTo(req.getHotelCode()).andBizDateBetween(DateUtils.strConverDateTime(req.getStartTime()),
        DateUtils.strConverDateTime(req.getEndTime()));
    example.setOrderByClause("biz_date ASC");
    List<AdsFinaDlEcoAppOwnerCoreD> list = biCoreDMapper.selectByExample(example);
    if (CollectionUtils.isEmpty(list)){
      throw new NormalException("暂无数据");
    }
    List<MultiTableListVo> result = new ArrayList<>();

    List<ModuleMenuVo> firstLevelList = dataBoards.stream().filter(
            v -> v.getPId().equals(vo.getId())
                && BusinessConditionEnum.isTableDataItem(v.getModuleCode())
                && (StringUtils.isEmpty(req.getCode()) || v.getModuleCode().equals(req.getCode())))
        .sorted(Comparator.comparing(ModuleMenuVo::getSort))
        .collect(Collectors.toList());
    for (ModuleMenuVo first : firstLevelList) {
      List<DataTableVo> tableList = new ArrayList<>();
      String moduleCode = first.getModuleCode();

      if (BusinessConditionEnum.BIZ_INCOME_TREND.getCode().equals(moduleCode)) {
        List<ClassificationVo> totalAmountList =  list.stream().map(s -> new ClassificationVo(String.valueOf(s.getTotalAmt()), DateUtil.formatDatOf(s.getBizDate()))).collect(Collectors.toList());
        tableList.add(DataTableVo.builder().code(BizIncomeTrendEnum.BIZ_IN_TOTAL_AMT.getCode()).name(BizIncomeTrendEnum.BIZ_IN_TOTAL_AMT.getName()).sort(BizIncomeTrendEnum.BIZ_IN_TOTAL_AMT.getSort()).voList(totalAmountList).build());

        List<ClassificationVo> totaRoomlAmountList =  list.stream().map(s -> new ClassificationVo(String.valueOf(s.getTotalRoomAmt()), DateUtil.formatDatOf(s.getBizDate()))).collect(Collectors.toList());
        tableList.add(DataTableVo.builder().code(BizIncomeTrendEnum.BIZ_IN_TOTAL_ROOM_AMT.getCode()).name(BizIncomeTrendEnum.BIZ_IN_TOTAL_ROOM_AMT.getName()).sort(BizIncomeTrendEnum.BIZ_IN_TOTAL_ROOM_AMT.getSort()).voList(totaRoomlAmountList).build());

        List<ClassificationVo> cateringAmtList =  list.stream().map(s -> new ClassificationVo(String.valueOf(s.getCateringAmt()), DateUtil.formatDatOf(s.getBizDate()))).collect(Collectors.toList());
        tableList.add(DataTableVo.builder().code(BizIncomeTrendEnum.BIZ_IN_CATERING_AMT.getCode()).name(BizIncomeTrendEnum.BIZ_IN_CATERING_AMT.getName()).sort(BizIncomeTrendEnum.BIZ_IN_CATERING_AMT.getSort()).voList(cateringAmtList).build());

        List<ClassificationVo> otherAmtList =  list.stream().map(s -> new ClassificationVo(String.valueOf(s.getOtherAmt()), DateUtil.formatDatOf(s.getBizDate()))).collect(Collectors.toList());
        tableList.add(DataTableVo.builder().code(BizIncomeTrendEnum.BIZ_IN_OTHER_AMT.getCode()).name(BizIncomeTrendEnum.BIZ_IN_OTHER_AMT.getName()).sort(BizIncomeTrendEnum.BIZ_IN_OTHER_AMT.getSort()).voList(otherAmtList).build());
      } else if (BusinessConditionEnum.BIZ_SOLD_TREND_CHANNEL.getCode().equals(moduleCode)){
        List<ClassificationVo> roomNightsNList =  list.stream().map(s -> new ClassificationVo(String.valueOf(s.getRoomNightsN()), DateUtil.formatDatOf(s.getBizDate()))).collect(Collectors.toList());
        tableList.add(DataTableVo.builder().code(BizSoldTrendEnum.BIZ_SOLD_ROOM.getCode()).name(BizSoldTrendEnum.BIZ_SOLD_ROOM.getName()).sort(BizSoldTrendEnum.BIZ_SOLD_ROOM.getSort()).voList(roomNightsNList).build());

        List<ClassificationVo> crsRoomNightsNList =  list.stream().map(s -> new ClassificationVo(String.valueOf(s.getCrsRoomNightsN()), DateUtil.formatDatOf(s.getBizDate()))).collect(Collectors.toList());
        tableList.add(DataTableVo.builder().code(BizSoldTrendEnum.BIZ_SOLD_CRS.getCode()).name(BizSoldTrendEnum.BIZ_SOLD_CRS.getName()).sort(BizSoldTrendEnum.BIZ_SOLD_CRS.getSort()).voList(crsRoomNightsNList).build());

        List<ClassificationVo> offlineRoomNightsNList =  list.stream().map(s -> new ClassificationVo(String.valueOf(s.getOfflineRoomNightsN()), DateUtil.formatDatOf(s.getBizDate()))).collect(Collectors.toList());
        tableList.add(DataTableVo.builder().code(BizSoldTrendEnum.BIZ_SOLD_OFFLINE.getCode()).name(BizSoldTrendEnum.BIZ_SOLD_OFFLINE.getName()).sort(BizSoldTrendEnum.BIZ_SOLD_OFFLINE.getSort()).voList(offlineRoomNightsNList).build());
      }else if (BusinessConditionEnum.BIZ_CHARGE_TREND_MEMBER.getCode().equals(moduleCode)){
        List<ClassificationVo> roomNightsNList =  list.stream().map(s -> new ClassificationVo(String.valueOf(s.getRoomNightsN()), DateUtil.formatDatOf(s.getBizDate()))).collect(Collectors.toList());
        tableList.add(DataTableVo.builder().code(BizSoldTrendEnum.BIZ_SOLD_ROOM.getCode()).name(BizSoldTrendEnum.BIZ_SOLD_ROOM.getName()).sort(BizSoldTrendEnum.BIZ_SOLD_ROOM.getSort()).voList(roomNightsNList).build());

        List<ClassificationVo> arrMemNightsNList =  list.stream().map(s -> new ClassificationVo(String.valueOf(s.getArrMemNightsN()), DateUtil.formatDatOf(s.getBizDate()))).collect(Collectors.toList());
        tableList.add(DataTableVo.builder().code(BizSoldTrendEnum.BIZ_SOLD_MEM.getCode()).name(BizSoldTrendEnum.BIZ_SOLD_MEM.getName()).sort(BizSoldTrendEnum.BIZ_SOLD_MEM.getSort()).voList(arrMemNightsNList).build());

        List<ClassificationVo> arrNonMemNightsNList =  list.stream().map(s -> new ClassificationVo(String.valueOf(s.getArrNonMemNightsN()), DateUtil.formatDatOf(s.getBizDate()))).collect(Collectors.toList());
        tableList.add(DataTableVo.builder().code(BizSoldTrendEnum.BIZ_SOLD_NON_MEM.getCode()).name(BizSoldTrendEnum.BIZ_SOLD_NON_MEM.getName()).sort(BizSoldTrendEnum.BIZ_SOLD_NON_MEM.getSort()).voList(arrNonMemNightsNList).build());
      }

      MultiTableListVo multiTableListVo = MultiTableListVo.builder()
          .moduleName(first.getModuleName())
          .moduleCode(moduleCode)
          .sort(first.getSort())
          .tableList(tableList)
          .build();
      result.add(multiTableListVo);
    }
    return result;
  }

  /**
   * 在手预订趋势
   *
   * @param req
   * @return
   */
  @Override
  public OnHandBookingTrendsVo onHandBookingTrends(DataBulletinBoardBo req) throws Exception {
    OnHandBookingTrendsVo result = new OnHandBookingTrendsVo();
    Integer userId = ThreadLocalHelper.getUser().getId();
    List<String> param = userInfoCommonService.getUserRights(
        userId, ThreadLocalHelper.getCompanyId(),
        UserRightsTypeEnum.APP);
    final String bookingTrendCode = DataBoardModuleEnum.BOOKING_TREND.name()
        .toLowerCase();
    if (CollectionUtils.isEmpty(param) || !param.contains(bookingTrendCode)) {
      throw new NormalException("用户权限不足,请联系管理员！");
    }

    List<String> userRoles = modUserCommonService.getUserRoles(userId);
    List<ModuleMenuVo> dataBoards = modNewDataBoardMapper.findModuleMenuDetailList(
        DataTypeEnum.HOTEL.name(), userRoles, userId);
    ModuleMenuVo vo = dataBoards.stream()
        .filter(v -> bookingTrendCode.equals(v.getModuleCode()))
        .findFirst().orElse(null);
    if (Objects.isNull(vo)) {
      throw new NormalException("用户权限不足,请联系管理员！");
    }
    // 默认查询15天
    Date start = StringUtils.isEmpty(req.getStartTime()) ? new Date() : DateUtils.strConverDateTime(req.getStartTime());
    Date end = StringUtils.isEmpty(req.getEndTime()) ? DateUtil.parseDayAdd(new Date(),15) : DateUtils.strConverDateTime(req.getEndTime());

    AdsFinaDlEcoAppOwnerCoreHExample example = new AdsFinaDlEcoAppOwnerCoreHExample();
    example.createCriteria().andHotelCodeEqualTo(req.getHotelCode()).andBizDateBetween(start, end);
    example.setOrderByClause("biz_date ASC");
    List<AdsFinaDlEcoAppOwnerCoreH> coreHList = biCoreHMapper.selectByExample(example);
    if (CollectionUtils.isEmpty(coreHList)){
      throw new NormalException("暂无数据");
    }
    List<BookingTrends> trendsList = new ArrayList<>();
    for (AdsFinaDlEcoAppOwnerCoreH coreH : coreHList) {
      BookingTrends trends = BookingTrends.builder()
          .date(DateUtil.formatDatOf(coreH.getBizDate()))
          .week(DateUtil.getWeekOfDate(coreH.getBizDate()))
          .roomAmt(String.valueOf(coreH.getRoomAmt()))
          .roomNightsN(String.valueOf(coreH.getRoomNightsN()))
          .investOcc(String.valueOf(coreH.getInvestOcc()))
          .tradeAdr(String.valueOf(coreH.getTradeAdr()))
          .build();
      trendsList.add(trends);
    }

    result.setDesc(vo.getDescName());
    result.setVoList(trendsList);
    return result;
  }

  /**
   * 结算账单
   *
   * @param req
   * @return
   */
  @Override
  public List<SettleBillVo> settleBill(SettleBillBo req) throws Exception {
    String month = req.getMonth();
    if (!DateUtil.isLessThanCurrentMonth(month)) {
      throw new NormalException("当前月份,暂无数据");
    }

    String hotelCode = req.getHotelCode();
    List<SettleBillFileRecords> recordsList = this.getSettleBillFileRecords(
        hotelCode, month, 0);
    if (CollectionUtils.isEmpty(recordsList)){
      log.warn("未查询到结算账单文件 酒店: {} 月份: {}", hotelCode, month);
      throw new NormalException("暂无数据");
    }
    
    List<SettleBillVo> result = new ArrayList<>(8);
    Integer userId = ThreadLocalHelper.getUser().getId();
    int hotelId = ThreadLocalHelper.getCompanyId();

    List<String> param = userInfoCommonService.getUserRights(
        userId, hotelId, UserRightsTypeEnum.APP);
    final String finalStatementCode = DataBoardModuleEnum.FINAL_STATEMENT.name()
        .toLowerCase();
    if (CollectionUtils.isEmpty(param) || !param.contains(finalStatementCode)) {
      throw new NormalException("用户权限不足,请联系管理员！");
    }

    List<String> userRoles = modUserCommonService.getUserRoles(userId);
    List<ModuleMenuVo> dataBoards = modNewDataBoardMapper.findModuleMenuDetailList(HotelTypeEnum.HOTEL.name(), userRoles,userId);
    ModuleMenuVo vo = dataBoards.stream()
        .filter(v -> finalStatementCode.equals(v.getModuleCode()))
        .findFirst().orElse(null);
    if (Objects.isNull(vo)) {
      throw new NormalException("用户权限不足,请联系管理员！");
    }

    AdsFinaDlEcoAppSettlementBillMExample example = new AdsFinaDlEcoAppSettlementBillMExample();
    example.createCriteria().andHotelCodeEqualTo(hotelCode).andBizMonthEqualTo(DateUtils.strConverDate(
        month));
    List<AdsFinaDlEcoAppSettlementBillM> billMList = biSettlementBillMMapper.selectByExample(example);
    if (CollectionUtils.isEmpty(billMList)){
      throw new NormalException("暂无数据");
    }
    AdsFinaDlEcoAppSettlementBillM bill = billMList.get(0);
    SimpleDateFormat sdf = new SimpleDateFormat(BaseConstants.FORMAT_MONTH);
    Date date = sdf.parse(month);
    HotelBillPdfVO pdfVO = convertHotelBillPdf(bill,date);
    //人员派遣费用
    pdfVO = fillHotelPdDebitAmt(bill, pdfVO,req.getHotelCode(), req.getMonth());

    List<ModuleMenuVo> firstLevelList = dataBoards.stream()
        .filter(v -> v.getPId().equals(vo.getId()))
        .sorted(Comparator.comparing(ModuleMenuVo::getSort))
        .collect(Collectors.toList());
    for (ModuleMenuVo menuVo : firstLevelList) {
      String code = menuVo.getModuleCode();
      List<SettleBillVo> childList = new ArrayList<>();
      SettleBillVo billVo = SettleBillVo.builder()
          .code(code)
          .name(menuVo.getModuleName())
          .desc(menuVo.getDescName())
          .sort(menuVo.getSort())
          .child(childList)
          .build();
      if (FinalStatementEnum.BILL_SETTLEMENT_AMT.getCode().equals(code)){
        billVo.setValue(pdfVO.getTotal());
      } else if (FinalStatementEnum.BILL_HOTEL_OVERVIEW.getCode().equals(code)){
        childList.add(SettleBillVo.builder().name("营业收入").desc(pdfVO.getRevenueRevenueDesc()).value(pdfVO.getRevenueRevenue()).build());
        childList.add(SettleBillVo.builder().name("运营RevPAR").desc(pdfVO.getRevPARDesc()).value(pdfVO.getRevPAR()).build());
        childList.add(SettleBillVo.builder().name("入住会员占比").desc(pdfVO.getCheckInMemberPerDesc()).value(pdfVO.getCheckInMemberPer()).build());
      } else if (FinalStatementEnum.BILL_GROUP_FEE.getCode().equals(code)){
        billVo.setValue(pdfVO.getPayGroupFee());

        childList.add(SettleBillVo.builder().name("品牌使用及营销费").value(pdfVO.getBrandFee()).build());
        childList.add(SettleBillVo.builder().name("基本管理费").value(pdfVO.getBasicFee()).build());
        childList.add(SettleBillVo.builder().name("奖励管理费").desc("每月预收，年度清算调整").value(pdfVO.getRewardManageFee()).build());
        childList.add(SettleBillVo.builder().name("百达屋佣金_净额").desc("百达屋订单总金额*8%(餐饮5%)-本店新会员减免-本店企业会员减免-阶段性营销减免-酒店券减免").value(pdfVO.getGwNetCommissionFee()).build());
        childList.add(SettleBillVo.builder().name("百达屋订单总金额").code("gwOrderAmt").desc(pdfVO.getGwOrderAmtDesc()).value(pdfVO.getGwOrderAmt()).build());
        childList.add(SettleBillVo.builder().name("百达屋佣金率").code("gwCommissionRate").value(pdfVO.getGwCommissionRate()).build());
      } else if (FinalStatementEnum.BILL_BDW_MEMBER_FEE.getCode().equals(code)){
        billVo.setValue(pdfVO.getBdwMemRightsFee());

        childList.add(SettleBillVo.builder().name("会员时光值代收款").desc(pdfVO.getMemTimvalFeeDesc()).value(pdfVO.getMemTimvalFee()).build());
        childList.add(SettleBillVo.builder().name("时光值兑换和房券结算").desc(pdfVO.getTimvalRoomCouponFeeDesc()).value(pdfVO.getTimvalRoomCouponFee()).build());
//        childList.add(SettleBillVo.builder().name("百达卡首单补贴").desc("2024年11月账单开始生效").value(pdfVO.getBdwCardFirstOrderSubsidyAmt()).build());
        childList.add(SettleBillVo.builder().name("会员法宝补贴").desc(pdfVO.getMemMagicSubsidyAmtDesc()).value(pdfVO.getMemMagicSubsidyAmt()).build());

      } else if (FinalStatementEnum.BILL_GROUP_WAIVER.getCode().equals(code)){
        billVo.setValue(pdfVO.getGwReduceCommissionFee());

        childList.add(SettleBillVo.builder().name("本店新会员减免").desc("本店24小时内发展新会员、本人百达屋预订且30分钟内本人入住免佣").value(pdfVO.getNewMemReduceFee()).build());
        childList.add(SettleBillVo.builder().name("本店企业会员减免").desc("本店发展的企业个人会员入住免佣").value(pdfVO.getCompanyMemReduceFee()).build());
        childList.add(SettleBillVo.builder().name("阶段性营销减免").desc("现阶段：阶梯减免政策").value(pdfVO.getStageActivityReduceFee()).build());
        childList.add(SettleBillVo.builder().name("酒店券减免").desc("百达屋订单使用酒店券预订订单免佣").value(pdfVO.getHotelCouponReduceFee()).build());

      } else if (FinalStatementEnum.BILL_PAY_GROUP_NET_FEE.getCode().equals(code)){
        billVo.setValue(pdfVO.getPayGroupNetFee());

      } else if (FinalStatementEnum.BILL_DEBIT_AMT.getCode().equals(code)){
        billVo.setValue(pdfVO.getDebitAmt());

        childList.add(SettleBillVo.builder().name("百达卡销售分润").desc(pdfVO.getBdwCardDivAmtDesc()).value(pdfVO.getBdwCardDivAmt()).build());
        childList.add(SettleBillVo.builder().name("百达屋代收代付").desc(pdfVO.getGwDebitAmtDesc()).value(pdfVO.getGwDebitAmt()).build());
        childList.add(SettleBillVo.builder().name("分销代收代付").desc(pdfVO.getDistriDebitAmtDesc()).value(pdfVO.getDistriDebitAmt()).build());
        childList.add(SettleBillVo.builder().name("分销渠道佣金代收代付").code("distriDebitCommissionFee").desc(pdfVO.getDistriDebitCommissionFeeDesc()).value(pdfVO.getDistriDebitCommissionFee()).build());
        childList.add(SettleBillVo.builder().name("OTA佣金率").code("distriCommissionRate").value(pdfVO.getDistriCommissionRate()).build());
        childList.add(SettleBillVo.builder().name("人员派遣费用").desc(pdfVO.getPdDebitAmtDesc()).value(pdfVO.getPdDebitAmt()).build());

      } else if (FinalStatementEnum.BILL_TOTAL_BALANCE.getCode().equals(code)){
        billVo.setDesc(pdfVO.getTotalRpBalanceAmtDesc());
        billVo.setValue(pdfVO.getTotalRpBalanceAmt());

      }
      result.add(billVo);
    }

    return result;
  }

  @Override
  public Boolean createHotelBill(SettleBillBo req, String hotelName) throws Exception {

    AdsFinaDlEcoAppSettlementBillMExample example = new AdsFinaDlEcoAppSettlementBillMExample();
    example.createCriteria().andHotelCodeEqualTo(req.getHotelCode()).andBizMonthEqualTo(DateUtils.strConverDate(req.getMonth()));
    List<AdsFinaDlEcoAppSettlementBillM> billMList = biSettlementBillMMapper.selectByExample(example);
    if (CollectionUtils.isEmpty(billMList)){
      log.info("[德胧生态酒店账单生成],hotelCode:{},没有对应的账单数据",req.getHotelCode());
      return false;
    }
    // 2. 查询账单文件记录用户MD5值
    SettleBillFileRecordsExample settleBillFileRecordsExample = new SettleBillFileRecordsExample();
    settleBillFileRecordsExample.createCriteria().andHotelCodeEqualTo(req.getHotelCode()).andBizMonthEqualTo(req.getMonth())
        .andDelFlagEqualTo(0);
    example.setOrderByClause("create_time DESC");
    List<SettleBillFileRecords> records = billFileRecordsMapper.selectByExample(settleBillFileRecordsExample);
    if (CollectionUtils.isNotEmpty(records)){
      for (SettleBillFileRecords record : records) {
        record.setDelFlag(1);
        record.setUpdateTime(new Date());
        billFileRecordsMapper.updateByPrimaryKey(record);
      }
    }
    AdsFinaDlEcoAppSettlementBillM bill = billMList.get(0);
    SimpleDateFormat sdf = new SimpleDateFormat(BaseConstants.FORMAT_MONTH);
    Date date = sdf.parse(req.getMonth());

    HotelBillPdfVO hotelBillPdfVO = convertHotelBillPdf(bill,date);
    //人员派遣费用
    hotelBillPdfVO = fillHotelPdDebitAmt(bill, hotelBillPdfVO,req.getHotelCode(), req.getMonth());
    String objectName = createBillFile(hotelBillPdfVO, req);

    //插入记录
    SettleBillFileRecords settleBillFileRecords = SettleBillFileRecords.builder()
        .hotelCode(req.getHotelCode())
        .bizMonth(req.getMonth())
        .fileName(DateUtil.formatDateByTem(date,"yyyy年MM月").concat(hotelName))
        .filePath(objectName)
        .userId(0)
        .md5Value("")
        .createTime(new Date())
        .updateTime(new Date())
        .delFlag(0)
        .build();
    return billFileRecordsMapper.insert(settleBillFileRecords) > 0;
  }

  /**
   * 获取结算账单文件md5值
   * 1. 判断有无账单权限
   * 2. 查询账单文件记录用户MD5值
   * 3. 不存在则根据酒店账单文件生成水印文件
   * 4. 上传到oss
   * 5. 新增文件记录并返回MD5
   *
   * @param req
   * @return
   */
  @Override
  public String getSettleBillMD5(SettleBillBo req) throws NormalException {
    UserInfoVO user = ThreadLocalHelper.getUser();
    Integer userId = user.getId();
    String mobile = user.getMobile();
    String month = req.getMonth();
    String hotelCode = req.getHotelCode();

    // 1. 判断有无账单权限
    int hotelId = ThreadLocalHelper.getCompanyId();
    List<String> param = userInfoCommonService.getUserRights(
        userId, hotelId, UserRightsTypeEnum.APP);
    final String finalStatementCode = DataBoardModuleEnum.FINAL_STATEMENT.name()
        .toLowerCase();
    if (CollectionUtils.isEmpty(param) || !param.contains(finalStatementCode)) {
      throw new NormalException("用户权限不足,请联系管理员！");
    }

    String lockKey = Tools.buildKey(BaseConstants.SETTLE_BILL_MD5_LOCK, hotelCode + month + userId);
    if(lockService.lock(lockKey,3000L)) {
      // 2. 查询账单文件记录用户MD5值
      SettleBillFileRecordsExample example = new SettleBillFileRecordsExample();
      example.createCriteria().andHotelCodeEqualTo(hotelCode).andBizMonthEqualTo(month)
          .andDelFlagEqualTo(0);
      example.setOrderByClause("create_time DESC");
      List<SettleBillFileRecords> records = billFileRecordsMapper.selectByExample(example);
      if (CollectionUtils.isEmpty(records)){
        throw new NormalException("暂无数据");
      }
      Map<Integer, SettleBillFileRecords> recordsMap = records.stream()
          .collect(Collectors.toMap(SettleBillFileRecords::getUserId, v -> v));
      SettleBillFileRecords userRecord = recordsMap.get(userId);
      if (Objects.nonNull(userRecord)){
        return userRecord.getMd5Value();
      }
      SettleBillFileRecords hotelRecord = recordsMap.get(0);
      String fileName = generateFileName(hotelCode,month,mobile);
      // 3. 生成水印文件
      Map<String,String> fileMap = generateWatermarkFile(hotelRecord.getFilePath(), user.getName() + mobile.substring(mobile.length()-4),fileName);
      if(MapUtils.isEmpty(fileMap)) {
        throw new NormalException("系统异常，请稍后再试");
      }
      // 5. 新增文件记录并返回MD5
      String md5Value = fileMap.get("md5Value");
      insertFileRecord(req.getHotelCode(), req.getMonth(), userId, "", md5Value, fileMap.get("filePath"));
      lockService.unlock(lockKey);
      return md5Value;
    }
    throw new NormalException("文件下载中，请稍后");
  }

  private String generateFileName(String hotelCode, String month, String phoneNumber) {
    String phoneLastFourDigits = phoneNumber.substring(phoneNumber.length() - 4);
    return String.format("%s_%s_%s.pdf", hotelCode, month.replace("-", ""), phoneLastFourDigits);
  }

  private Map<String,String> generateWatermarkFile(String filePath, String waterMarkText, String fileName)
      throws NormalException {
    InputStream inputStream = billFileStorageUtil.getInputStream(filePath);
    try {
      Map<String,String> map = new HashMap<>();
      int size = 10;
      // 获取项目路径
      String projectPath = System.getProperty("user.dir");
      // 创建临时文件
      File tempFile = File.createTempFile("prefix", ".pdf", new File(projectPath));

      PdfDocument pdfDoc = new PdfDocument(new PdfReader(inputStream), new PdfWriter(tempFile));
      float angle = (float) Math.toRadians(35);
      PdfFont pdfFont = PdfFontFactory.createFont(hotelBillConfig.getFontUrl(), PdfEncodings.IDENTITY_H);
      Paragraph paragraph = new Paragraph(waterMarkText).setFont(pdfFont).setFontSize(size);
      PdfExtGState gs = new PdfExtGState();
      gs.setFillOpacity(0.2f);
      PageSize pageSize = pdfDoc.getDefaultPageSize();

      // 文本宽度(用于计算间隔)
      float textWidth = pdfFont.getWidth(waterMarkText, 100);
      // 用正弦定理计算出水印高度
      float labelHeight = (float) Math.sin(angle) * textWidth;
      // 用勾股计算出旋转后的水印宽度
      float labelWidth = (float) Math.sqrt(Math.pow(textWidth, 2) - Math.pow(labelHeight, 2));
      for (int i = 1; i <= pdfDoc.getNumberOfPages(); i++) {
        PdfCanvas over = new PdfCanvas(pdfDoc.getPage(i));
        over.setFillColor(ColorConstants.BLACK);
        over.setExtGState(gs);
        for (int x = 0; x < pageSize.getWidth() / labelWidth * 2; x++) {
          for (int y = 0; y < pageSize.getHeight() / labelHeight * 1.25; y++) {
            float pX = x * labelWidth / 2;
            float pY = y * labelHeight * .8f + x * labelHeight * .2f;
            Canvas canvas = new Canvas(over, pageSize);
            canvas.showTextAligned(paragraph,
                pX, pY, i,
                TextAlignment.CENTER, VerticalAlignment.MIDDLE, angle);
            canvas.close();
          }
        }
      }
      pdfDoc.close();
      //上传到oss
      String objectName = uploadHotelBillToOss(tempFile,fileName);
      map.put("filePath",objectName);
      map.put("md5Value",calculateMD5(objectName));
      tempFile.delete();
      return map;
    } catch (Exception e) {
      log.info("生成账单失败",e);
      return null;
    }
  }

  private String calculateMD5(String objectName)
      throws IOException, NoSuchAlgorithmException, NormalException {

    MessageDigest md = MessageDigest.getInstance("MD5");
    InputStream inputStream = billFileStorageUtil.getInputStream(objectName);
    byte[] buffer = new byte[8192];
    int bytesRead;

    while ((bytesRead = inputStream.read(buffer)) != -1) {
      md.update(buffer, 0, bytesRead);
    }
    byte[] md5Bytes = md.digest();

    StringBuilder sb = new StringBuilder();
    for (byte md5Byte : md5Bytes) {
      sb.append(Integer.toHexString((md5Byte & 0xFF) | 0x100).substring(1, 3));
    }
    inputStream.close();
    return sb.toString();
  }


  private String uploadToOSS(File file) {
    // 实现上传文件到OSS的逻辑
    return "oss://bucket-name/path/to/" + file.getName();
  }
  private void insertFileRecord(String hotelCode, String month, Integer userId, String fileName,
      String md5Value, String ossFilePath) {
    // 实现插入文件记录的逻辑
    SettleBillFileRecords record = SettleBillFileRecords.builder()
        .hotelCode(hotelCode)
        .bizMonth(month)
        .fileName(fileName)
        .filePath(ossFilePath)
        .userId(userId)
        .md5Value(md5Value)
        .createTime(new Date())
        .updateTime(new Date())
        .delFlag(0)
        .build();
    billFileRecordsMapper.insert(record);
  }

  /**
   * 下载结算账单
   *
   * @param response
   */
  @Override
  public void downloadSettleBill(HttpServletResponse response, String hotelCode, String month) throws NormalException {
    UserInfoVO user = ThreadLocalHelper.getUser();
    Integer userId = user.getId();
    String mobile = user.getMobile();

    // 1. 判断有无账单权限
    int hotelId = ThreadLocalHelper.getCompanyId();
    List<String> param = userInfoCommonService.getUserRights(
        userId, hotelId, UserRightsTypeEnum.APP);
    final String finalStatementCode = DataBoardModuleEnum.FINAL_STATEMENT.name()
        .toLowerCase();
    if (CollectionUtils.isEmpty(param) || !param.contains(finalStatementCode)) {
      throw new NormalException("用户权限不足,请联系管理员！");
    }

    // 2. 查询账单文件记录用户MD5值
    SettleBillFileRecordsExample example = new SettleBillFileRecordsExample();
    example.createCriteria().andHotelCodeEqualTo(hotelCode).andBizMonthEqualTo(month)
        .andDelFlagEqualTo(0).andUserIdEqualTo(userId);
    List<SettleBillFileRecords> records = billFileRecordsMapper.selectByExample(example);
    if (CollectionUtils.isEmpty(records)){
      throw new NormalException("暂无数据");
    }
    SettleBillFileRecords record = records.get(0);
    InputStream inputStream = billFileStorageUtil.getInputStream(record.getFilePath());
    try {
      //获取 oss 文件
      response.setContentType("application/octet-stream;charset=utf-8");
      response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
      response.setHeader("Content-Disposition", "attachment;filename="
          + URLEncoder.encode(record.getMd5Value() + ".pdf", StandardCharsets.UTF_8));
      BufferedInputStream buff = new BufferedInputStream(inputStream);
      BufferedOutputStream out = new BufferedOutputStream(response.getOutputStream());
      byte[] car = new byte[1024];
      int l = 0;
      while ((l = buff.read(car)) != -1) {
        out.write(car, 0, l);
      }
      // 关闭流
      buff.close();
      out.close();
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
  }

  /**
   * 获取结算账单最新日期
   *
   * @param req
   * @return
   */
  @Override
  public String getLatestDate(SettleBillBo req) {
    List<SettleBillFileRecords> result = this.getSettleBillFileRecords(req.getHotelCode(), req.getMonth(), 0);

    // 返回最新日期
    if (CollectionUtils.isEmpty(result)) {
      return DateUtil.formatDatOf(DateUtil.addMonth(new Date(),-1));
    } else {
      return result.get(0).getBizMonth();
    }
  }

  private List<SettleBillFileRecords> getSettleBillFileRecords(String hotelCode, String month,
      Integer userId) {
    SettleBillFileRecordsExample example = new SettleBillFileRecordsExample();
    Criteria criteria = example.createCriteria();
    criteria.andHotelCodeEqualTo(hotelCode)
        .andUserIdEqualTo(userId)
        .andDelFlagEqualTo(0);
    if (StringUtils.isNotBlank(month)) {
      criteria.andBizMonthEqualTo(month);
    }
    example.setOrderByClause("biz_month DESC");
    return billFileRecordsMapper.selectByExample(example);
  }

  /**
   * 获取今日已售房分析
   *
   * @param type
   * @return
   */
  @Override
  public TodayReportVo todayRoomsSoldData(String type) throws Exception {
    TodayReportVo result = new TodayReportVo();
    Integer userId = ThreadLocalHelper.getUser().getId();
    int hotelId = ThreadLocalHelper.getCompanyId();
    String hotelCode = hotelInfoDao.queryById(hotelId).getHotelCode();
    List<String> param = userInfoCommonService.getUserRights(
        userId, hotelId, UserRightsTypeEnum.APP);
    final String roomsSoldTodayCode = DataBoardModuleEnum.ROOMS_SOLD_TODAY.name()
        .toLowerCase();
    if (CollectionUtils.isEmpty(param) || !param.contains(roomsSoldTodayCode)) {
      throw new NormalException("用户权限不足,请联系管理员！");
    }

    AdsFinaDlEcoAppOwnerCoreHExample example = new AdsFinaDlEcoAppOwnerCoreHExample();
    example.createCriteria().andHotelCodeEqualTo(hotelCode).andBizDateEqualTo(new Date());
    List<AdsFinaDlEcoAppOwnerCoreH> coreHList = biCoreHMapper.selectByExample(example);
    if (CollectionUtils.isEmpty(coreHList)){
      throw new NormalException("暂无数据");
    }
    AdsFinaDlEcoAppOwnerCoreH todayData = coreHList.get(0);
    List<HomeDataDetailsVo> voList = new ArrayList<>();

    voList.add(HomeDataDetailsVo.builder().code(TodayRoomSoldEnum.TODAY_SOLD_ROOM.getCode()).name(TodayRoomSoldEnum.TODAY_SOLD_ROOM.getName()).value(String.valueOf(todayData.getCrsRoomNightsN() + todayData.getOfflineRoomNightsN())).sort(TodayRoomSoldEnum.TODAY_SOLD_ROOM.getSort()).build());
    voList.add(HomeDataDetailsVo.builder().code(TodayRoomSoldEnum.TODAY_SOLD_CRS.getCode()).name(TodayRoomSoldEnum.TODAY_SOLD_CRS.getName()).value(String.valueOf(todayData.getCrsRoomNightsN())).sort(TodayRoomSoldEnum.TODAY_SOLD_CRS.getSort()).build());
    voList.add(HomeDataDetailsVo.builder().code(TodayRoomSoldEnum.TODAY_SOLD_OFFLINE.getCode()).name(TodayRoomSoldEnum.TODAY_SOLD_OFFLINE.getName()).value(String.valueOf(todayData.getOfflineRoomNightsN())).sort(TodayRoomSoldEnum.TODAY_SOLD_OFFLINE.getSort()).build());

    result.setUpdateTime(DateUtils.dateToStr(todayData.getDataTime()));
    result.setHotelCode(hotelCode);
    result.setShowFlag(true);
    result.setBizData(DateUtil.formatDatOf(new Date()));
    result.setHomeDataDetailsVoList(voList);
    return result;
  }

  /**
   * 今日数据看板
   * @param type
   * @return
   * @throws Exception
   */
  @Override
  public TodayReportVo todayCoreData(String type) throws Exception {
    TodayReportVo result = new TodayReportVo();
    Integer userId = ThreadLocalHelper.getUser().getId();
    int hotelId = ThreadLocalHelper.getCompanyId();
    String hotelCode = hotelInfoDao.queryById(hotelId).getHotelCode();
    List<String> param = userInfoCommonService.getUserRights(
        userId, hotelId, UserRightsTypeEnum.APP);
    final String dataBoardTodayCode = DataBoardModuleEnum.DATA_BOARD_TODAY.name()
        .toLowerCase();
    if (CollectionUtils.isEmpty(param) || !param.contains(dataBoardTodayCode)) {
      throw new NormalException("用户权限不足,请联系管理员！");
    }

    List<String> userRoles = modUserCommonService.getUserRoles(userId);
    List<ModuleMenuVo> dataBoards = modNewDataBoardMapper.findModuleMenuDetailList(type, userRoles,userId);
    ModuleMenuVo vo = dataBoards.stream()
        .filter(v -> dataBoardTodayCode.equals(v.getModuleCode()))
        .findFirst().orElse(null);
    if (Objects.isNull(vo)) {
      throw new NormalException("用户权限不足,请联系管理员！");
    }

    AdsDataBoardTodayPo todayPo = biCoreHMapper.queryByHotelCodeAndDate(hotelCode, new Date());
    if (Objects.isNull(todayPo)){
      throw new NormalException("暂无数据");
    }
    List<HomeDataDetailsVo> voList = new ArrayList<>();
    List<ModuleMenuVo> firstLevelList = dataBoards.stream()
        .filter(v -> v.getPId().equals(vo.getId()))
        .sorted(Comparator.comparing(ModuleMenuVo::getSort))
        .collect(Collectors.toList());
    for (ModuleMenuVo first : firstLevelList) {
      HomeDataDetailsVo detailsVo = HomeDataDetailsVo.builder()
          .name(first.getModuleName())
          .desc(first.getDescName())
          .code(first.getModuleCode())
          .pCode(vo.getModuleCode())
          .sort(first.getSort())
          .unit(first.getUnit())
          .build();
      // 赋值操作
      this.fillValue(todayPo, detailsVo);
//      this.fillNameWithUnit(detailsVo);

      voList.add(detailsVo);
    }

    result.setUpdateTime(com.shands.mod.main.util.hs.DateUtils.dateToString(todayPo.getDataTime(), com.shands.mod.main.util.hs.DateUtils.DATE_FORMAT_MM_DD_HM_2));
    result.setHotelCode(hotelCode);
    result.setShowFlag(true);
    result.setBizData(DateUtil.formatDatOf(new Date()));
    result.setHomeDataDetailsVoList(voList);
    return result;
  }

  private void fillValue(AdsDataBoardTodayPo todayPo, HomeDataDetailsVo detailsVo){
    String code = detailsVo.getCode();
    Object value = todayPo.getValueByFieldName(code);
    if (Objects.nonNull(value)) {
      detailsVo.setValue(String.valueOf(value));
      if (value instanceof Double) {
        Double doubleValue = (Double) value;
        if (Double.compare(doubleValue, 10_000d) > 0) {
          BigDecimal TEN_THOUSAND = new BigDecimal("10000");
          BigDecimal numOfWan = new BigDecimal(doubleValue).divide(TEN_THOUSAND, 2, RoundingMode.HALF_UP);
          // 需要展示为千分位的格式
          detailsVo.setValue(ThousandSeparatorUtil.format(numOfWan));
          detailsVo.setUnit("万");
        } else {
          detailsVo.setValue(ThousandSeparatorUtil.format(doubleValue));
        }
      }
    }
  }

  private HotelBillPdfVO convertHotelBillPdf(AdsFinaDlEcoAppSettlementBillM bill,Date date){
    HotelBillPdfVO hotelBillPdfVO = new HotelBillPdfVO();


    hotelBillPdfVO.setHotelName(bill.getHotelName());
    hotelBillPdfVO.setBillTime(DateUtil.formatDateByTem(date,"yyyy年MM月"));
    hotelBillPdfVO.setSendTIme(DateUtil.formatDateByTem(new Date(),"yyyy年MM月dd日"));
    hotelBillPdfVO.setRevenueRevenue(Tools.formatAmountWithCommas(bill.getTotalAmt()));
    hotelBillPdfVO.setRevPAR(Tools.formatAmountWithCommas(bill.getTradeRevpar()));
    hotelBillPdfVO.setCheckInMemberPer(bill.getArrMemRate() + CommonConstant.PERCENT_SYMBOL);
    hotelBillPdfVO.setRevenueRevenueDesc(String.format("客房（%s）+餐饮（%s）+其他（%s）",Tools.formatAmountWithCommas(bill.getRoomTotalAmt())
        ,Tools.formatAmountWithCommas(bill.getCateringAmt()),Tools.formatAmountWithCommas(bill.getOtherAmt())));
    hotelBillPdfVO.setRevPARDesc(String.format("运营OCC（%s）,运营ADR（%s）",bill.getTradeOcc() + CommonConstant.PERCENT_SYMBOL
        ,Tools.formatAmountWithCommas(bill.getTradeAdr())));
    hotelBillPdfVO.setCheckInMemberPerDesc(String.format("首住会员占比%s，复购会员占比%s",bill.getNewArrMemRate() + CommonConstant.PERCENT_SYMBOL
        ,bill.getReArrMemRate() + CommonConstant.PERCENT_SYMBOL));
    hotelBillPdfVO.setTitle(String.format("德胧集团%s账单",DateUtil.formatDateByTem(date,"yyyy年MM月")));
    hotelBillPdfVO.setTotal(Tools.formatAmountWithCommas(bill.getSettlementAmt()));

    hotelBillPdfVO.setBrandFee(Tools.formatAmountWithCommas(bill.getBrandFee()));
    hotelBillPdfVO.setBasicFee(Tools.formatAmountWithCommas(bill.getBasicFee()));
    hotelBillPdfVO.setRewardManageFee(Tools.formatAmountWithCommas(bill.getRewardManageFee()));
    hotelBillPdfVO.setGwNetCommissionFee(Tools.formatAmountWithCommas(bill.getGwNetCommissionFee()));
    hotelBillPdfVO.setGwOrderAmt(Tools.formatAmountWithCommas(bill.getGwOrderAmt()));
    hotelBillPdfVO.setGwOrderAmtDesc(String.format("预付（%s）+ 现付（%s）",Tools.formatAmountWithCommas(bill.getGwOrderPreAmt()),Tools.formatAmountWithCommas(bill.getGwOrderLateAmt())));
    hotelBillPdfVO.setGwCommissionRate(bill.getGwCommissionRate() + CommonConstant.PERCENT_SYMBOL);
    hotelBillPdfVO.setPayGroupFee(Tools.formatAmountWithCommas(bill.getPayGroupFee()));
    hotelBillPdfVO.setMemTimvalFee(Tools.formatAmountWithCommas(bill.getMemTimvalFee()));
    hotelBillPdfVO.setMemTimvalFeeDesc(getMemTimvalFeeDesc(bill));
    hotelBillPdfVO.setTimvalRoomCouponFee(Tools.formatAmountWithCommas(bill.getTimvalRoomCouponFee()));
    hotelBillPdfVO.setTimvalRoomCouponFeeDesc(getTimvalRoomCouponFeeDesc(bill));
    hotelBillPdfVO.setBdwCardFirstOrderSubsidyAmt(Tools.formatAmountWithCommas(bill.getBdwCardFirstOrderSubsidyAmt()));
    hotelBillPdfVO.setMemMagicSubsidyAmt(Tools.formatAmountWithCommas(bill.getMemMagicSubsidyAmt()));
    hotelBillPdfVO.setMemMagicSubsidyAmtDesc(String.format("共补贴%s个订单，平均每单补贴%s元",bill.getMemMagicSubsidyOrderN(),Tools.formatAmountWithCommas(bill.getMemMagicSubsidyAvgAmt())));
    hotelBillPdfVO.setBdwMemRightsFee(Tools.formatAmountWithCommas(bill.getBdwMemRightsFee()));
    hotelBillPdfVO.setNewMemReduceFee(Tools.formatAmountWithCommas(bill.getNewMemReduceFee()));
    hotelBillPdfVO.setCompanyMemReduceFee(Tools.formatAmountWithCommas(bill.getCompanyMemReduceFee()));
    hotelBillPdfVO.setStageActivityReduceFee(Tools.formatAmountWithCommas(bill.getStageActivityReduceFee()));
    hotelBillPdfVO.setHotelCouponReduceFee(Tools.formatAmountWithCommas(bill.getHotelCouponReduceFee()));
    hotelBillPdfVO.setGwReduceCommissionFee(Tools.formatAmountWithCommas(bill.getGwReduceCommissionFee()));
    hotelBillPdfVO.setPayGroupNetFee(Tools.formatAmountWithCommas(bill.getPayGroupNetFee()));
    hotelBillPdfVO.setBdwCardDivAmt(Tools.formatAmountWithCommas(bill.getBdwCardDivAmt()));
    hotelBillPdfVO.setBdwCardDivAmtDesc(String.format("共售卡%s张",bill.getBdBdwCardCnt()));
    hotelBillPdfVO.setGwDebitAmtDesc(String.format("客房（%s） + 餐饮及其他（%s）",Tools.formatAmountWithCommas(bill.getGwRoomDebitAmt()),Tools.formatAmountWithCommas(bill.getGwCateringDebitAmt())));
    hotelBillPdfVO.setGwDebitAmt(Tools.formatAmountWithCommas(bill.getGwDebitAmt()));
    hotelBillPdfVO.setDistriDebitAmt(Tools.formatAmountWithCommas(bill.getDistriDebitAmt()));
    hotelBillPdfVO.setDistriDebitAmtDesc(String.format("OTA（%s） + TMC（%s）",Tools.formatAmountWithCommas(bill.getOtaDebitAmt()),Tools.formatAmountWithCommas(bill.getTmcDebitAmt())));
    hotelBillPdfVO.setDistriDebitCommissionFee(Tools.formatAmountWithCommas(bill.getDistriDebitCommissionFee()));
    hotelBillPdfVO.setDistriDebitCommissionFeeDesc(this.getDistriDebitCommissionFeeDesc(bill));
    hotelBillPdfVO.setDistriCommissionRate(bill.getDistriCommissionRate() + CommonConstant.PERCENT_SYMBOL);
    hotelBillPdfVO.setPdDebitAmt(Tools.formatAmountWithCommas(bill.getPdDebitAmt()));
    //hotelBillPdfVO.setPdDebitAmtDesc(String.format("此处呈现%s份数据",DateUtil.getLastDayOfMonth("yyyy年MM月",1,date)));
    hotelBillPdfVO.setDebitAmt(Tools.formatAmountWithCommas(bill.getDebitAmt()));
    hotelBillPdfVO.setTotalRpBalanceAmt(Tools.formatAmountWithCommas(bill.getTotalRpBalanceAmt()));
    hotelBillPdfVO.setTotalRpBalanceAmtDesc(String.format("截止%s，累计酒店与集团往来的所有费用项的应收/应付余额，正数为酒店应收，负数为酒店应付，每月10号、20号进行结算（节假日顺延）",DateUtil.getLastDayOfMonth("MM月dd日",0,date)));
    return hotelBillPdfVO;
  }

  private String getDistriDebitCommissionFeeDesc(AdsFinaDlEcoAppSettlementBillM bill){
    if (bill.showDistriDebitCommissionFeeDesc()){
      return String.format("其中%s入住人为会员，若改道百达屋下单，佣金率可降至3%%，预计百达屋+分销渠道总计可节省%s元",bill.getDistriArrMemRate() + CommonConstant.PERCENT_SYMBOL,Tools.formatAmountWithCommas(bill.getDistriToGwReduceAmt()));
    }
    return null;
  }

  private String getMemTimvalFeeDesc(AdsFinaDlEcoAppSettlementBillM bill){
    if((bill.getMemTimvalFee() == 0 && bill.getMemTimvalFeeRate() != 0) || (bill.getMemTimvalFee() != 0 && bill.getMemTimvalFeeRate() == 0)) {
      return null;
    }
    return String.format("实际收取比例%s%%",bill.getMemTimvalFeeRate());
  }

  private String getTimvalRoomCouponFeeDesc(AdsFinaDlEcoAppSettlementBillM bill){
    if(bill.getTimvalRoomCouponFee() > 0 && bill.getTimvalRoomCouponNightsN() == 0) {
      return null;
    }
    return String.format("共结算%s间夜，平均ADR%s",bill.getTimvalRoomCouponNightsN(),Tools.formatAmountWithCommas(bill.getTimvalRoomCouponAdr()));
  }




  private String createBillFile(HotelBillPdfVO hotelBillPdfVO, SettleBillBo req){
    try{
      String inputFilePath = hotelBillConfig.getBillTemplateUrl();
      String fontFilePath = hotelBillConfig.getFontUrl();
      String boldFontFilePath = hotelBillConfig.getBoldFontUrl();
      // 获取项目路径
      String projectPath = System.getProperty("user.dir");
      // 创建临时文件
      File tempFile = File.createTempFile("prefix", ".pdf", new File(projectPath));

      PdfFont pdfFont = PdfFontFactory.createFont(fontFilePath, PdfEncodings.IDENTITY_H);
      PdfFont boldPdfFont = PdfFontFactory.createFont(boldFontFilePath, PdfEncodings.IDENTITY_H);

      PdfDocument pdfDoc = new PdfDocument(new PdfReader(inputFilePath), new PdfWriter(tempFile));
      PdfAcroForm form = PdfAcroForm.getAcroForm(pdfDoc, false);

      for (HotelBillPdfEnum value : HotelBillPdfEnum.values()) {
        // 获取要替换的文本域
        PdfFormField field = form.getField(value.getKey());
        if (field != null) {
          Field methodField = hotelBillPdfVO.getClass().getDeclaredField(value.getProperty());
          methodField.setAccessible(true);
          String propertyValue = (String) methodField.get(hotelBillPdfVO);
          if (!value.getBlod()) {
            field.setFont(pdfFont);
          } else {
            field.setFont(boldPdfFont);
          }
          field.setValue(StringUtils.isBlank(propertyValue) ? "" : propertyValue);
          field.setColor(value.getDeviceRgb());
          field.setFontSize(value.getFontSize());
        }
      }
      form.flattenFields(); // 如果需要将文本域值替换为静态文本，可使用此行代码

      // 生成渠道房型收益PDF
      createChannelRoomRevenuePdf(pdfDoc, req);

      pdfDoc.close();

      //上传到oss
      String objectName = uploadHotelBillToOss(tempFile,String.format("%s账单%s.pdf",hotelBillPdfVO.getHotelName(),hotelBillPdfVO.getBillTime(),
          System.currentTimeMillis()));
      log.info("[德胧生态酒店账单生成],objectName:{}",objectName);
      tempFile.delete();
      return objectName;
    } catch (Exception e) {
      return null;
    }
  }

  private void createChannelRoomRevenuePdf(PdfDocument pdfDoc, SettleBillBo req) {
    try {
      // 1、查询渠道房型收益数据
      List<AdsTradeHotelChannelAdrFeeM> channelAdrFeeList = adsTradeHotelChannelAdrFeeMMapper.selectByHotelCodeAndYearMonth(
          req.getHotelCode(), req.getMonth());
      
      if (CollectionUtils.isEmpty(channelAdrFeeList)) {
        log.warn("未查询到渠道房型收益数据，酒店: {}, 月份: {}", req.getHotelCode(), req.getMonth());
        return;
      }

      // 2、处理查询数据，排序
      List<AdrReportRowVo> reportData = processChannelAdrData(channelAdrFeeList);
      
      if (CollectionUtils.isEmpty(reportData)) {
        log.warn("处理后的渠道房型收益数据为空");
        return;
      }

      // 3、获取酒店名称
      String hotelName = channelAdrFeeList.get(0).getHotelName();

      // 4、生成PDF报表并添加到现有PDF文档
      String yearMonth = DateUtil.formatDateByTem(DateUtils.strConverDate(req.getMonth()), "yyyy年MM月");
      hotelAdrReportPdfService.addHotelAdrReportToDocument(pdfDoc, hotelName, yearMonth, reportData);
      
      log.info("渠道房型收益PDF已成功添加到账单，酒店: {}, 月份: {}", req.getHotelCode(), req.getMonth());
      
    } catch (Exception e) {
      log.error("生成渠道房型收益PDF失败，酒店: {}, 月份: {}", req.getHotelCode(), req.getMonth(), e);
    }
  }

  /**
   * 处理渠道房型收益数据，按照需求排序并格式化
   */
  private List<AdrReportRowVo> processChannelAdrData(List<AdsTradeHotelChannelAdrFeeM> channelAdrFeeList) {
    // 按房型分组
    Map<String, List<AdsTradeHotelChannelAdrFeeM>> roomTypeMap = channelAdrFeeList.stream()
        .collect(Collectors.groupingBy(AdsTradeHotelChannelAdrFeeM::getRoomTypeCode));

    List<AdrReportRowVo> result = new ArrayList<>();

    // 过滤出有效房型（百达屋-会员价 或 百达屋-百达卡 的实收ADR不为空）
    Map<String, List<AdsTradeHotelChannelAdrFeeM>> validRoomTypes = roomTypeMap.entrySet().stream()
        .filter(entry -> hasValidBdwData(entry.getValue()))
        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

    if (validRoomTypes.isEmpty()) {
      return result;
    }

    // 计算房型排序分数并排序
    List<Map.Entry<String, List<AdsTradeHotelChannelAdrFeeM>>> sortedRoomTypes = validRoomTypes.entrySet().stream()
        .sorted((entry1, entry2) -> {
          double score1 = calculateRoomTypeSortScore(entry1.getValue());
          double score2 = calculateRoomTypeSortScore(entry2.getValue());
          return Double.compare(score2, score1); // 降序排列
        })
        .collect(Collectors.toList());

    // 为每个房型生成报表数据
    for (Map.Entry<String, List<AdsTradeHotelChannelAdrFeeM>> roomTypeEntry : sortedRoomTypes) {
      String roomTypeCode = roomTypeEntry.getKey();
      List<AdsTradeHotelChannelAdrFeeM> roomChannelData = roomTypeEntry.getValue();
      
      // 添加房型汇总行
      String roomTypeName = roomChannelData.get(0).getRoomTypeDescript();
      if (StringUtils.isBlank(roomTypeName)) {
        roomTypeName = roomTypeCode;
      }
      
      // 查找房型汇总数据（渠道类别为空字符串）
      Optional<AdsTradeHotelChannelAdrFeeM> summaryDataOpt = roomChannelData.stream()
          .filter(data -> StrUtil.isBlank(data.getChannelCategory()))
          .findFirst();
      
      if (summaryDataOpt.isPresent()) {
        AdrReportRowVo roomTypeRow = createDataRow(summaryDataOpt.get(), roomTypeName, true, false, false);
        result.add(roomTypeRow);
      } else {
        // 如果没有找到汇总数据，创建空行(兜底)
        AdrReportRowVo roomTypeRow = new AdrReportRowVo(roomTypeName, "", "", "",
                "", "", "", "",
                true, false, false);
        result.add(roomTypeRow);
      }

      // 查找百达屋最大ADR差值的渠道
      ChannelTypeEnum maxAdrChannelType = findMaxAdrDifferenceChannel(roomChannelData);
      
      // 按 bigType 分组并生成数据
      generateChannelGroupRows(result, roomChannelData, maxAdrChannelType);
    }

    return result;
  }

  /**
   * 按 bigType 分组生成渠道汇总行和子渠道行
   */
  private void generateChannelGroupRows(List<AdrReportRowVo> result, 
                                       List<AdsTradeHotelChannelAdrFeeM> roomChannelData,
                                       ChannelTypeEnum maxAdrChannelType) {
    // 按 bigType 分组
    List<String> bigTypes = Arrays.asList("百达屋", "OTA", "协议客户");
    
    for (String bigType : bigTypes) {
      // 查找 bigType 汇总数据
      Optional<AdsTradeHotelChannelAdrFeeM> bigTypeDataOpt = roomChannelData.stream()
          .filter(data -> bigType.equals(data.getChannelCategory()))
          .findFirst();
      
      if (bigTypeDataOpt.isPresent()) {
        // 添加 bigType 汇总行
        AdrReportRowVo bigTypeRow = createDataRow(bigTypeDataOpt.get(), bigType, false, false, false);
        result.add(bigTypeRow);
        
        // 获取该 bigType 下的所有子渠道
        List<ChannelTypeEnum> subChannels = Arrays.stream(ChannelTypeEnum.values())
            .filter(channel -> bigType.equals(channel.getBigType()))
            .collect(Collectors.toList());
        
        // 添加子渠道行
        for (ChannelTypeEnum subChannel : subChannels) {
          Optional<AdsTradeHotelChannelAdrFeeM> channelDataOpt = roomChannelData.stream()
              .filter(data -> subChannel.getTypeName().equals(data.getChannelCategory()))
              .findFirst();
          
          if (channelDataOpt.isPresent()) {
            AdsTradeHotelChannelAdrFeeM channelData = channelDataOpt.get();
            boolean isMaxDifference = (maxAdrChannelType != null && subChannel == maxAdrChannelType);
            
            AdrReportRowVo channelRow = createDataRow(channelData, channelData.getChannelCategory(), false, true, isMaxDifference);
            result.add(channelRow);
          }
        }
      }
    }
  }

  /**
   * 检查房型是否有有效的百达屋数据
   */
  private boolean hasValidBdwData(List<AdsTradeHotelChannelAdrFeeM> roomChannelData) {
    boolean hasBdwPrice = roomChannelData.stream()
        .anyMatch(data -> ChannelTypeEnum.BDW_PRICE.getTypeName().equals(data.getChannelCategory()));
    
    boolean hasBdwCard = roomChannelData.stream()
        .anyMatch(data -> ChannelTypeEnum.BDW_CARD.getTypeName().equals(data.getChannelCategory()));
    
    return hasBdwPrice || hasBdwCard;
  }

  /**
   * 计算房型排序分数 - 按百达屋已售间夜数排序
   */
  private double calculateRoomTypeSortScore(List<AdsTradeHotelChannelAdrFeeM> roomChannelData) {
    // 获取百达屋渠道的已售间夜总数
    return roomChannelData.stream()
        .filter(data -> {
          for (ChannelTypeEnum channel : ChannelTypeEnum.values()) {
            if ("百达屋".equals(channel.getBigType()) && 
                channel.getTypeName().equals(data.getChannelCategory())) {
              return true;
            }
          }
          return false;
        })
        .filter(data -> data.getRoomsN() != null)
        .mapToDouble(AdsTradeHotelChannelAdrFeeM::getRoomsN)
        .sum();
  }

  /**
   * 查找百达屋ADR最大的渠道
   */
  private ChannelTypeEnum findMaxAdrDifferenceChannel(List<AdsTradeHotelChannelAdrFeeM> roomChannelData) {
    // 百达屋-会员价
    Optional<AdsTradeHotelChannelAdrFeeM> bdwPrice = roomChannelData.stream()
        .filter(data -> ChannelTypeEnum.BDW_PRICE.getTypeName().equals(data.getChannelCategory()))
        .findFirst();

    // 百达屋-百达卡
    Optional<AdsTradeHotelChannelAdrFeeM> bdwCard = roomChannelData.stream()
        .filter(data -> ChannelTypeEnum.BDW_CARD.getTypeName().equals(data.getChannelCategory()))
        .findFirst();

    if (bdwPrice.isPresent() && bdwCard.isPresent()) {
      return bdwPrice.get().getAdr() > bdwCard.get().getAdr() ? ChannelTypeEnum.BDW_PRICE : ChannelTypeEnum.BDW_CARD;
    } else if (bdwPrice.isPresent()) {
      return ChannelTypeEnum.BDW_PRICE;
    } else {
      return ChannelTypeEnum.BDW_CARD;
    }
  }

  /**
   * 通用数据行创建方法
   * @param data 数据源
   * @param displayName 显示名称
   * @param isHighlighted 是否高亮
   * @param isSubItem 是否为子项
   * @param isMaxAdrDifference 是否最大ADR差值
   */
  private AdrReportRowVo createDataRow(AdsTradeHotelChannelAdrFeeM data, String displayName,
                                      boolean isHighlighted, boolean isSubItem, boolean isMaxAdrDifference) {
    String marketPrice = data.getTotalRackRateAdr() != null ?
        ThousandSeparatorUtil.format(data.getTotalRackRateAdr()) : "";
    String soldRooms = data.getRoomsN() != null ?
            ThousandSeparatorUtil.format(data.getRoomsN()) : "";
    String commissionRoomFee = data.getRoomsTaxAmtFee() != null ?
            ThousandSeparatorUtil.format(data.getRoomsTaxAmtFee()) : "";
    String commissionAdr = data.getAdrFee() != null ?
            ThousandSeparatorUtil.format(data.getAdrFee()) : "";
    String actualCommissionRate = (data.getFeeRate() != null) ?
            ThousandSeparatorUtil.format(data.getFeeRate()) + "%" : "";
    String actualRoomFee = data.getRoomsTaxAmt() != null ?
            ThousandSeparatorUtil.format(data.getRoomsTaxAmt()) : "";
    String actualAdr = data.getAdr() != null ?
            ThousandSeparatorUtil.format(data.getAdr()) : "";

    return new AdrReportRowVo(displayName, marketPrice, soldRooms, commissionRoomFee,
                             commissionAdr, actualCommissionRate, actualRoomFee, actualAdr,
                             isHighlighted, isSubItem, isMaxAdrDifference);
  }


  private HotelBillPdfVO fillHotelPdDebitAmt(AdsFinaDlEcoAppSettlementBillM bill,HotelBillPdfVO hotelBillPdfVO,String hotelCode, String dateStr) throws Exception {
    double pdDebitAmt = 0 - getHotelPdDebitAmt(hotelCode,dateStr);
    hotelBillPdfVO.setPdDebitAmt(Tools.formatAmountWithCommas(pdDebitAmt));
    if(pdDebitAmt != 0) {
      hotelBillPdfVO.setDebitAmt(Tools.formatAmountWithCommas(bill.getDebitAmt() + pdDebitAmt));
      hotelBillPdfVO.setTotal(Tools.formatAmountWithCommas(bill.getSettlementAmt() + pdDebitAmt));
    }
    return hotelBillPdfVO;
  }

  private double getHotelPdDebitAmt(String hotelCode, String dateStr) throws Exception {
    String year = dateStr.substring(0,dateStr.indexOf("-"));
    String month = Integer.parseInt(dateStr.substring(dateStr.indexOf("-") + 1)) + "";

    ModEmailRecord modEmailRecord = new ModEmailRecord();
    modEmailRecord.setYear(year);
    modEmailRecord.setMonth(month);
    modEmailRecord.setHotelCode(hotelCode);
    List<ModEmailRecord> modEmailRecordList = modEmailRecordMapper.queryAll(modEmailRecord);
    if(CollectionUtils.isEmpty(modEmailRecordList)) {
      return 0;
    }
    List<String> nameList = new ArrayList<>();
    modEmailRecordList.sort(Comparator.comparing(ModEmailRecord::getCreateTime).reversed());
    double pdDebitAmt = 0;
    for (ModEmailRecord emailRecord : modEmailRecordList) {
      if(!nameList.contains(emailRecord.getName())) {
        pdDebitAmt += Double.parseDouble(BaiDaWuRSAUtils.decrypt(emailRecord.getTotalRemittance(), BaiDaWuRSAUtils.getPrivateKey(BaiDaWuRSAUtils.DL_RSA_PRIVATE_KEY)));
        nameList.add(emailRecord.getName());
      }
    }
    return pdDebitAmt;
  }


  /**
   * 获取结算账单概览
   * @param month 月份 yyyy-MM
   * @return 结算账单概览数据
   */
  @Override
  public SettleBillOverviewVo getSettleBillOverview(String month) {
    if (!DateUtil.isLessThanCurrentMonth(month)) {
      throw new ServiceException("当前月份,暂无数据");
    }

    Integer userId = ThreadLocalHelper.getUser().getId();
    int hotelId = ThreadLocalHelper.getCompanyId();
    String hotelCode = hotelInfoDao.queryById(hotelId).getHotelCode();
    Date monthDate = null;
    try {
      monthDate = DateUtils.strConverDate(month);
    } catch (ParseException e) {
      throw new RuntimeException(e);
    }

    List<String> param = userInfoCommonService.getUserRights(userId, hotelId, UserRightsTypeEnum.APP);
    final String finalStatementCode = DataBoardModuleEnum.FINAL_STATEMENT.name().toLowerCase();
    if (CollectionUtils.isEmpty(param) || !param.contains(finalStatementCode)) {
      throw new ServiceException("用户权限不足,请联系管理员！");
    }

    List<SettleBillFileRecords> recordsList = this.getSettleBillFileRecords(hotelCode, month, 0);
    if (CollectionUtils.isEmpty(recordsList)) {
      log.warn("未查询到结算账单文件 酒店: {} 月份: {}", hotelCode, month);
      throw new ServiceException("暂无数据");
    }

    List<String> userRoles = modUserCommonService.getUserRoles(userId);
    List<ModuleMenuVo> moduleMenuVos = modNewDataBoardMapper.findModuleMenuDetailList(HotelTypeEnum.HOTEL.name(), userRoles, userId);

    SettleBillOverviewVo res = new SettleBillOverviewVo();

    // 3. 查询结算账单数据
    AdsFinaDlEcoAppSettlementBillMDto settleBillData = biSettlementBillMMapper.selectSettleBillOverviewData(hotelCode, monthDate);

    calculateSettlementAmount(settleBillData, hotelCode, month);

    // 4. 设置本期月结账单数据
    setSettleBillModuleData(DataBoardModuleEnum.MONTHLY_SETTLEMENT_BILL, res.getMonthlySettlementBill(),
            moduleMenuVos, settleBillData);

    // 5. 设置渠道房型收益数据
    setSettleBillModuleData(DataBoardModuleEnum.REVENUE_OF_CHANNEL_ROOM, res.getRevenueOfChannelRoom(),
            moduleMenuVos, settleBillData);

    return res;
  }

  /**
   * 设置结算账单模块数据
   * @param moduleEnum 模块枚举
   * @param targetDataVo 目标数据对象
   * @param moduleMenuVos 模块菜单列表
   * @param settleBillData 结算账单数据
   */
  private void setSettleBillModuleData(DataBoardModuleEnum moduleEnum, BaseStatisticsDataVo targetDataVo,
                                       List<ModuleMenuVo> moduleMenuVos, AdsFinaDlEcoAppSettlementBillMDto settleBillData) {
    // 查找对应的模块菜单
    ModuleMenuVo bigModuleMenu = moduleMenuVos.stream()
            .filter(v -> moduleEnum.name().equals(v.getModuleCode()))
            .findFirst().orElse(null);

    // 如果没有权限，设置为不显示
    if (bigModuleMenu == null) {
      targetDataVo.setShowFlag(false);
      return;
    }

    // 获取指标列表
    List<ModuleMenuVo> indicatorList = moduleMenuVos.stream()
            .filter(v -> Objects.equals(bigModuleMenu.getId(), v.getPId()))
            .sorted(Comparator.comparing(ModuleMenuVo::getSort))
            .collect(Collectors.toList());

    // 设置基本信息
    targetDataVo.setShowFlag(true);
    targetDataVo.setTitle(bigModuleMenu.getModuleName());
    targetDataVo.setTitleDesc(bigModuleMenu.getDescName());

    // 构建数据列表
    List<HomeDataDetailsVo> dataList = new ArrayList<>();
    for (ModuleMenuVo indicator : indicatorList) {
      HomeDataDetailsVo detailsVo = HomeDataDetailsVo.builder()
              .name(indicator.getModuleName())
              .desc(indicator.getDescName())
              .code(indicator.getModuleCode())
              .pCode(bigModuleMenu.getModuleCode())
              .sort(indicator.getSort())
              .unit(indicator.getUnit())
              .build();

      // 设置数据值
      fillSettleBillDataValue(settleBillData, detailsVo);

      // 如果是OTA佣金比例，需要判断是否大于百达屋佣金比例才显示
      if ("distriCommissionRate".equals(detailsVo.getCode())) {
        boolean shouldShow = shouldShowOtaCommissionRate(settleBillData);
        if (!shouldShow) {
          continue; // 跳过这个指标
        }
      }

      dataList.add(detailsVo);
    }

    targetDataVo.setDataList(dataList);
  }

  /**
   * 填充结算账单数据值
   * @param data 结算账单数据
   * @param detailsVo 指标详情
   */
  private void fillSettleBillDataValue(AdsFinaDlEcoAppSettlementBillMDto data, HomeDataDetailsVo detailsVo) {
    if (data == null) {
      return;
    }

    String code = detailsVo.getCode();
    Object value = data.getValueByFieldName(code);
    if (Objects.nonNull(value)) {
      if (value instanceof Double) {
        Double valueDouble = (Double) value;
        if (StrUtil.equals(detailsVo.getUnit(), "元")) {
          detailsVo.setValue(Tools.formatAmountWithCommas(valueDouble));
        } else if (StrUtil.equals(detailsVo.getUnit(), CommonConstant.PERCENT_SYMBOL)) {
          detailsVo.setValue(ThousandSeparatorUtil.format(valueDouble) + CommonConstant.PERCENT_SYMBOL);
        }
      }
      else {
        // 对于其他类型，直接使用字符串值
        detailsVo.setValue(String.valueOf(value));
      }
    }

  }

  /**
   * 计算本期应结金额
   * @param data 结算账单数据
   * @param hotelCode 酒店代码
   * @param month 月份
   * @return 本期应结金额
   */
  private void calculateSettlementAmount(AdsFinaDlEcoAppSettlementBillMDto data, String hotelCode, String month) {
    if (data == null || data.getSettlementAmt() == null) {
      return;
    }
    double pdDebitAmt = 0;
    try {
      pdDebitAmt = getHotelPdDebitAmt(hotelCode, month);
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      data.setBillSettlementAmt(data.getSettlementAmt());
    }
    data.setBillSettlementAmt(data.getSettlementAmt() - pdDebitAmt);
  }



  /**
   * 判断是否应该显示OTA佣金比例
   * @param data 结算账单数据
   * @return 是否显示
   */
  private boolean shouldShowOtaCommissionRate(AdsFinaDlEcoAppSettlementBillMDto data) {
    if (data == null || data.getDistriCommissionRate()== null || data.getGwCommissionRate() == null) {
      return false;
    }
    return data.getDistriCommissionRate() > data.getGwCommissionRate();
  }

  /**
   * 上传oss
   * @param file
   * @return
   */
  private String uploadHotelBillToOss(File file, String fileName) throws Exception{
    String objectName = "mod/hotelBill/" + fileName ;
    billFileStorageUtil.putInputStream(objectName, new FileInputStream(file));
    return objectName;
  }
}
