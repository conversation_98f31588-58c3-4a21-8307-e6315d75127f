package com.shands.mod.main.service.workorder.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.shands.mod.dao.mapper.UserMapper;
import com.shands.mod.dao.mapper.hs.HotelServiceMapper;
import com.shands.mod.dao.mapper.hs.HsWaringMapper;
import com.shands.mod.dao.model.User;
import com.shands.mod.dao.model.hs.HotelService;
import com.shands.mod.dao.model.hs.HsWaring;
import com.shands.mod.dao.model.hs.WorkOrder;
import com.shands.mod.dao.model.hs.enums.HotelServiceType;
import com.shands.mod.dao.model.hs.enums.WorkOrderStateEnum;
import com.shands.mod.dao.model.req.hs.TrueFalseEnums;
import com.shands.mod.dao.model.req.hs.remind.RemindAddReq;
import com.shands.mod.dao.model.req.hs.remind.RemindQueryReq;
import com.shands.mod.dao.model.req.hs.remind.SendUsers;
import com.shands.mod.dao.model.res.hs.remind.RemindRes;
import com.shands.mod.exception.NormalException;
import com.shands.mod.main.service.workorder.IWaringService;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.vo.PageVO;
import com.shands.mod.vo.ResultVO;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service("waringService")
@Transactional(rollbackFor = Exception.class)
public class WaringServiceImpl implements IWaringService {

  private final HsWaringMapper waringMapper;

  private final UserMapper userMapper;

  @Autowired
  private HotelServiceMapper hotelServiceMapper;

  public WaringServiceImpl(HsWaringMapper waringMapper, UserMapper userMapper) {
    this.waringMapper = waringMapper;
    this.userMapper = userMapper;
  }

  @Override
  public ResultVO add(RemindAddReq remindAddReq) throws NormalException {
    HsWaring waring = new HsWaring();
    reqToDb(remindAddReq, waring);
    int i = waringMapper.insertSelective(waring);
    if (i < 0) {
      throw new NormalException("预警添加失败");
    }
    return ResultVO.success("添加成功");
  }

  @Override
  public ResultVO query(RemindQueryReq remindQueryReq, PageVO pageVO) {
    PageHelper.startPage(pageVO.getPageNo(), pageVO.getPageSize());
    if (remindQueryReq.getCompanyId() == null) {
      remindQueryReq.setCompanyId(ThreadLocalHelper.getCompanyId());
    }

    List<HsWaring> query = waringMapper.query(remindQueryReq);
    PageInfo tPageInfo = new PageInfo<>(query);
    List<RemindRes> remindResList = dbtoRes(tPageInfo.getList());
    tPageInfo.setList(remindResList);
    return ResultVO.success(tPageInfo);
  }

  @Override
  public ResultVO editor(RemindAddReq remindAddReq) throws NormalException {
    HsWaring waring = waringMapper.selectByPrimaryKey(remindAddReq.getId());
    if (waring != null) {
      waring.setAcceptDept(remindAddReq.getDeptId());
      waring.setAmount(calcTime(remindAddReq.getAmount(), remindAddReq.getTimeType()));
      waring.setOrderStatus(remindAddReq.getOrderStatus());
      waring.setSendSms(remindAddReq.getSendSms());
      waring.setServiceType(String.valueOf(remindAddReq.getServiceType()));
      waring.setStatus(remindAddReq.getStatus());
      waring.setUpdateTime(new Date());
      waring.setUpdateUser(ThreadLocalHelper.getUser().getId());

      //      `SERVICE_TYPE`, `ORDER_STATUS`, `COMPANY_ID`, `GROUP_ID`
      HsWaring db =
          waringMapper.uniqueConstraint(
              remindAddReq.getServiceType(),
              remindAddReq.getOrderStatus(),
              remindAddReq.getCompanyId(),
              remindAddReq.getGroupId());

      if (db != null && !waring.getId().equals(db.getId())) {
        throw new NormalException("服务类型+工单状态在一个企业内只能有一个提醒时间");
      }

      int i = waringMapper.updateByPrimaryKeySelective(waring);
      if (i < 0) {
        throw new NormalException("预警修改失败");
      }
    }
    return ResultVO.success("预警修改成功");
  }

  @Override
  public ResultVO byId(Integer id) {
    HsWaring waring = waringMapper.selectByPrimaryKey(id);
    return ResultVO.success(waring);
  }

  @Override
  public ResultVO editorStatus(Integer id) throws NormalException {
    HsWaring waring = waringMapper.selectByPrimaryKey(id);
    if (waring != null) {
      boolean b = TrueFalseEnums.intToBoolean(waring.getStatus());
      if (b) {
        waring.setStatus(TrueFalseEnums.FALSE.getCode());
      } else {
        waring.setStatus(TrueFalseEnums.TRUE.getCode());
      }
      int i = waringMapper.updateByPrimaryKeySelective(waring);
      if (i < 0) {
        throw new NormalException("预警状态修改失败");
      }
    }
    return ResultVO.success();
  }

  private List<RemindRes> dbtoRes(List<HsWaring> list) {
    List<RemindRes> remindResList = new ArrayList<>();
    for (HsWaring hsWaring : list) {
      RemindRes remindRes = new RemindRes();
      HotelService hotelService =
          hotelServiceMapper.selectByPrimaryKey(Integer.parseInt(hsWaring.getServiceType()));
      if (hotelService != null) {

        remindRes.setServiceTypeName(
            HotelServiceType.stringToEnum(hotelService.getServiceType()).getCnName());
      }
      remindRes.setAmount(hsWaring.getAmount());
      remindRes.setStatus(hsWaring.getStatus());
      String[] split = hsWaring.getAcceptUser().split(",");
      List<SendUsers> sendUsersList = new ArrayList<>();
      for (String id : split) {
        SendUsers sendUsers = new SendUsers();
        User user = userMapper.selectByPrimaryKey(Integer.parseInt(id));
        sendUsers.setId(user.getId());
        sendUsers.setName(user.getName());
        sendUsers.setPhone(user.getMobile());
        sendUsers.setDvid(user.getDeviceId());
        sendUsersList.add(sendUsers);
      }
      remindRes.setOrderStatus(hsWaring.getOrderStatus());
      remindRes.setSendUsers(sendUsersList);
      remindRes.setId(hsWaring.getId());
      remindRes.setSendSms(hsWaring.getSendSms());
      remindRes.setServiceType(hsWaring.getServiceType());
      remindResList.add(remindRes);
    }
    return remindResList;
  }

  private void reqToDb(RemindAddReq remindAddReq, HsWaring waring) {
    waring.setServiceType(remindAddReq.getServiceType().toString());
    Integer time = remindAddReq.getAmount();
    String type = remindAddReq.getTimeType();
    time = calcTime(time, type);

    waring.setAmount(time);
    waring.setOrderStatus(
        WorkOrderStateEnum.intToOrderStateEnum(remindAddReq.getOrderStatus()).getCode());
    waring.setAcceptDept(remindAddReq.getDeptId());
    waring.setSendSms(remindAddReq.getSendSms());
    waring.setStatus(remindAddReq.getStatus());
    waring.setCompanyId(remindAddReq.getCompanyId());
    waring.setGroupId(remindAddReq.getGroupId());
    waring.setCreateUser(remindAddReq.getUserId());
    waring.setCreateTime(new Date());
    waring.setUpdateUser(remindAddReq.getUserId());
    waring.setUpdateTime(new Date());
  }

  @NotNull
  private Integer calcTime(Integer time, String type) {
    if ("m".equalsIgnoreCase(type)) {
      // 分钟
      time = time * 60;
    } else {
      // 小时
      time = time * 60 * 60;
    }
    return time;
  }

  /** redis 中的提醒时间 */
  @Data
  @NoArgsConstructor
  private static class RemindTimeInRedis {
    private Long time;
    private HsWaring waring;
  }

  /** 工单提醒redis 实体 */
  @Data
  @NoArgsConstructor
  public static class WorkOrderLabel {

    /** 工单id */
    private String workOrderId;

    /** 工单最后操作时间,对应一个状态 */
    private Long endTime;

    private String groupId;
    private String companyId;
    private WorkOrder workOrder;
    private RemindTimeInRedis remindTimeInRedis;
    private String workOrderType;
    private Integer workOrderInt;

    private Integer accDeptUserId;

    /** -1 表示不存在 */
    private Integer deptId = -1;
  }
}
