package com.shands.mod.main.enums;


import com.shands.mod.util.BaseConstants;

/**
 * 验证码
 */
public enum ValidateCodeEnum {

  LOGIN(1, "验证码登录", "验证码:%s，您正在登录德胧生态App，如非本人操作请忽略", BaseConstants.CACHE_LOGIN_CODE, 60),

  SECURITY_VERIFICATION(2, "安全验证", "验证码:%s，您正在登录德胧生态App，如非本人操作请忽略", BaseConstants.CACHE_SECURITY_CODE, 60);

  private Integer type;

  private String desc;

  private String content;

  private String redisKeyPre;

  private Integer expireSeconds;

  public Integer getType() {
    return type;
  }

  public void setType(Integer type) {
    this.type = type;
  }

  public String getDesc() {
    return desc;
  }

  public void setDesc(String desc) {
    this.desc = desc;
  }

  public String getContent() {
    return content;
  }

  public void setContent(String content) {
    this.content = content;
  }

  public String getRedisKeyPre() {
    return redisKeyPre;
  }

  public Integer getExpireSeconds() {
    return expireSeconds;
  }

  public void setExpireSeconds(Integer expireSeconds) {
    this.expireSeconds = expireSeconds;
  }

  public void setRedisKeyPre(String redisKeyPre) {
    this.redisKeyPre = redisKeyPre;
  }

  ValidateCodeEnum(Integer type, String desc, String content, String redisKeyPre,
      Integer expireSeconds) {
    this.type = type;
    this.desc = desc;
    this.content = content;
    this.redisKeyPre = redisKeyPre;
    this.expireSeconds = expireSeconds;
  }

  public static ValidateCodeEnum getByType(Integer type) {
    for (ValidateCodeEnum validateCodeEnum : ValidateCodeEnum.values()) {
      if (validateCodeEnum.type.equals(type)) {
        return validateCodeEnum;
      }
    }
    return null;
  }

}
