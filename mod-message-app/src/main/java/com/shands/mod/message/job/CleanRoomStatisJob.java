package com.shands.mod.message.job;

import com.shands.mod.message.service.IMessageCleanRoomService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-07-08
 * @description 清扫任务数据统计（每天中午12点）
 */

@Slf4j
@Component
public class CleanRoomStatisJob {

  @Resource
  private IMessageCleanRoomService cleanRoomService;

  @XxlJob("cleanStatisticsByDay")
  public ReturnT<String> statisticsByDay(String param) throws Exception{

    if (cleanRoomService.statisticsByDay(param)){
      return new ReturnT<>(ReturnT.SUCCESS_CODE, "每日做房数据统计成功");
    }
    return new ReturnT<>(ReturnT.FAIL_CODE, "每日做房数据统计失败");
  }

  @XxlJob("cleanStatisticsByMonth")
  public ReturnT<String> statisticsByMonth(String param) throws Exception{

    if (cleanRoomService.statisticsByMonth(param)){
      return new ReturnT<>(ReturnT.SUCCESS_CODE, "每月做房数据统计成功");
    }
    return new ReturnT<>(ReturnT.FAIL_CODE, "每月做房数据统计失败");
  }

  @XxlJob("closeOrderJob")
  public ReturnT<String> closeOrderJob(String param) throws Exception{

    if (cleanRoomService.closeOrderJob()>0){
      return new ReturnT<>(ReturnT.SUCCESS_CODE, "每天关闭未完成工单成功");
    }
    return new ReturnT<>(ReturnT.FAIL_CODE, "每天关闭未完成工单失败");
  }

}
