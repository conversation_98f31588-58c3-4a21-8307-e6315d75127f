package com.delonix.bi.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class AdsPowerDlEcoAppEmployeePerfDExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public AdsPowerDlEcoAppEmployeePerfDExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andHotelCodeIsNull() {
            addCriterion("hotel_code is null");
            return (Criteria) this;
        }

        public Criteria andHotelCodeIsNotNull() {
            addCriterion("hotel_code is not null");
            return (Criteria) this;
        }

        public Criteria andHotelCodeEqualTo(String value) {
            addCriterion("hotel_code =", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeNotEqualTo(String value) {
            addCriterion("hotel_code <>", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeGreaterThan(String value) {
            addCriterion("hotel_code >", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeGreaterThanOrEqualTo(String value) {
            addCriterion("hotel_code >=", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeLessThan(String value) {
            addCriterion("hotel_code <", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeLessThanOrEqualTo(String value) {
            addCriterion("hotel_code <=", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeLike(String value) {
            addCriterion("hotel_code like", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeNotLike(String value) {
            addCriterion("hotel_code not like", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeIn(List<String> values) {
            addCriterion("hotel_code in", values, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeNotIn(List<String> values) {
            addCriterion("hotel_code not in", values, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeBetween(String value1, String value2) {
            addCriterion("hotel_code between", value1, value2, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeNotBetween(String value1, String value2) {
            addCriterion("hotel_code not between", value1, value2, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andBizDateIsNull() {
            addCriterion("biz_date is null");
            return (Criteria) this;
        }

        public Criteria andBizDateIsNotNull() {
            addCriterion("biz_date is not null");
            return (Criteria) this;
        }

        public Criteria andBizDateEqualTo(Date value) {
            addCriterionForJDBCDate("biz_date =", value, "bizDate");
            return (Criteria) this;
        }

        public Criteria andBizDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("biz_date <>", value, "bizDate");
            return (Criteria) this;
        }

        public Criteria andBizDateGreaterThan(Date value) {
            addCriterionForJDBCDate("biz_date >", value, "bizDate");
            return (Criteria) this;
        }

        public Criteria andBizDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("biz_date >=", value, "bizDate");
            return (Criteria) this;
        }

        public Criteria andBizDateLessThan(Date value) {
            addCriterionForJDBCDate("biz_date <", value, "bizDate");
            return (Criteria) this;
        }

        public Criteria andBizDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("biz_date <=", value, "bizDate");
            return (Criteria) this;
        }

        public Criteria andBizDateIn(List<Date> values) {
            addCriterionForJDBCDate("biz_date in", values, "bizDate");
            return (Criteria) this;
        }

        public Criteria andBizDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("biz_date not in", values, "bizDate");
            return (Criteria) this;
        }

        public Criteria andBizDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("biz_date between", value1, value2, "bizDate");
            return (Criteria) this;
        }

        public Criteria andBizDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("biz_date not between", value1, value2, "bizDate");
            return (Criteria) this;
        }

        public Criteria andEmployeeNcIdIsNull() {
            addCriterion("employee_nc_id is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeNcIdIsNotNull() {
            addCriterion("employee_nc_id is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeNcIdEqualTo(String value) {
            addCriterion("employee_nc_id =", value, "employeeNcId");
            return (Criteria) this;
        }

        public Criteria andEmployeeNcIdNotEqualTo(String value) {
            addCriterion("employee_nc_id <>", value, "employeeNcId");
            return (Criteria) this;
        }

        public Criteria andEmployeeNcIdGreaterThan(String value) {
            addCriterion("employee_nc_id >", value, "employeeNcId");
            return (Criteria) this;
        }

        public Criteria andEmployeeNcIdGreaterThanOrEqualTo(String value) {
            addCriterion("employee_nc_id >=", value, "employeeNcId");
            return (Criteria) this;
        }

        public Criteria andEmployeeNcIdLessThan(String value) {
            addCriterion("employee_nc_id <", value, "employeeNcId");
            return (Criteria) this;
        }

        public Criteria andEmployeeNcIdLessThanOrEqualTo(String value) {
            addCriterion("employee_nc_id <=", value, "employeeNcId");
            return (Criteria) this;
        }

        public Criteria andEmployeeNcIdLike(String value) {
            addCriterion("employee_nc_id like", value, "employeeNcId");
            return (Criteria) this;
        }

        public Criteria andEmployeeNcIdNotLike(String value) {
            addCriterion("employee_nc_id not like", value, "employeeNcId");
            return (Criteria) this;
        }

        public Criteria andEmployeeNcIdIn(List<String> values) {
            addCriterion("employee_nc_id in", values, "employeeNcId");
            return (Criteria) this;
        }

        public Criteria andEmployeeNcIdNotIn(List<String> values) {
            addCriterion("employee_nc_id not in", values, "employeeNcId");
            return (Criteria) this;
        }

        public Criteria andEmployeeNcIdBetween(String value1, String value2) {
            addCriterion("employee_nc_id between", value1, value2, "employeeNcId");
            return (Criteria) this;
        }

        public Criteria andEmployeeNcIdNotBetween(String value1, String value2) {
            addCriterion("employee_nc_id not between", value1, value2, "employeeNcId");
            return (Criteria) this;
        }

        public Criteria andHotelNameIsNull() {
            addCriterion("hotel_name is null");
            return (Criteria) this;
        }

        public Criteria andHotelNameIsNotNull() {
            addCriterion("hotel_name is not null");
            return (Criteria) this;
        }

        public Criteria andHotelNameEqualTo(String value) {
            addCriterion("hotel_name =", value, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameNotEqualTo(String value) {
            addCriterion("hotel_name <>", value, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameGreaterThan(String value) {
            addCriterion("hotel_name >", value, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameGreaterThanOrEqualTo(String value) {
            addCriterion("hotel_name >=", value, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameLessThan(String value) {
            addCriterion("hotel_name <", value, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameLessThanOrEqualTo(String value) {
            addCriterion("hotel_name <=", value, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameLike(String value) {
            addCriterion("hotel_name like", value, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameNotLike(String value) {
            addCriterion("hotel_name not like", value, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameIn(List<String> values) {
            addCriterion("hotel_name in", values, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameNotIn(List<String> values) {
            addCriterion("hotel_name not in", values, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameBetween(String value1, String value2) {
            addCriterion("hotel_name between", value1, value2, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameNotBetween(String value1, String value2) {
            addCriterion("hotel_name not between", value1, value2, "hotelName");
            return (Criteria) this;
        }

        public Criteria andEmployeeNameIsNull() {
            addCriterion("employee_name is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeNameIsNotNull() {
            addCriterion("employee_name is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeNameEqualTo(String value) {
            addCriterion("employee_name =", value, "employeeName");
            return (Criteria) this;
        }

        public Criteria andEmployeeNameNotEqualTo(String value) {
            addCriterion("employee_name <>", value, "employeeName");
            return (Criteria) this;
        }

        public Criteria andEmployeeNameGreaterThan(String value) {
            addCriterion("employee_name >", value, "employeeName");
            return (Criteria) this;
        }

        public Criteria andEmployeeNameGreaterThanOrEqualTo(String value) {
            addCriterion("employee_name >=", value, "employeeName");
            return (Criteria) this;
        }

        public Criteria andEmployeeNameLessThan(String value) {
            addCriterion("employee_name <", value, "employeeName");
            return (Criteria) this;
        }

        public Criteria andEmployeeNameLessThanOrEqualTo(String value) {
            addCriterion("employee_name <=", value, "employeeName");
            return (Criteria) this;
        }

        public Criteria andEmployeeNameLike(String value) {
            addCriterion("employee_name like", value, "employeeName");
            return (Criteria) this;
        }

        public Criteria andEmployeeNameNotLike(String value) {
            addCriterion("employee_name not like", value, "employeeName");
            return (Criteria) this;
        }

        public Criteria andEmployeeNameIn(List<String> values) {
            addCriterion("employee_name in", values, "employeeName");
            return (Criteria) this;
        }

        public Criteria andEmployeeNameNotIn(List<String> values) {
            addCriterion("employee_name not in", values, "employeeName");
            return (Criteria) this;
        }

        public Criteria andEmployeeNameBetween(String value1, String value2) {
            addCriterion("employee_name between", value1, value2, "employeeName");
            return (Criteria) this;
        }

        public Criteria andEmployeeNameNotBetween(String value1, String value2) {
            addCriterion("employee_name not between", value1, value2, "employeeName");
            return (Criteria) this;
        }

        public Criteria andEmployeeDeptIsNull() {
            addCriterion("employee_dept is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeDeptIsNotNull() {
            addCriterion("employee_dept is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeDeptEqualTo(String value) {
            addCriterion("employee_dept =", value, "employeeDept");
            return (Criteria) this;
        }

        public Criteria andEmployeeDeptNotEqualTo(String value) {
            addCriterion("employee_dept <>", value, "employeeDept");
            return (Criteria) this;
        }

        public Criteria andEmployeeDeptGreaterThan(String value) {
            addCriterion("employee_dept >", value, "employeeDept");
            return (Criteria) this;
        }

        public Criteria andEmployeeDeptGreaterThanOrEqualTo(String value) {
            addCriterion("employee_dept >=", value, "employeeDept");
            return (Criteria) this;
        }

        public Criteria andEmployeeDeptLessThan(String value) {
            addCriterion("employee_dept <", value, "employeeDept");
            return (Criteria) this;
        }

        public Criteria andEmployeeDeptLessThanOrEqualTo(String value) {
            addCriterion("employee_dept <=", value, "employeeDept");
            return (Criteria) this;
        }

        public Criteria andEmployeeDeptLike(String value) {
            addCriterion("employee_dept like", value, "employeeDept");
            return (Criteria) this;
        }

        public Criteria andEmployeeDeptNotLike(String value) {
            addCriterion("employee_dept not like", value, "employeeDept");
            return (Criteria) this;
        }

        public Criteria andEmployeeDeptIn(List<String> values) {
            addCriterion("employee_dept in", values, "employeeDept");
            return (Criteria) this;
        }

        public Criteria andEmployeeDeptNotIn(List<String> values) {
            addCriterion("employee_dept not in", values, "employeeDept");
            return (Criteria) this;
        }

        public Criteria andEmployeeDeptBetween(String value1, String value2) {
            addCriterion("employee_dept between", value1, value2, "employeeDept");
            return (Criteria) this;
        }

        public Criteria andEmployeeDeptNotBetween(String value1, String value2) {
            addCriterion("employee_dept not between", value1, value2, "employeeDept");
            return (Criteria) this;
        }

        public Criteria andMemAppNightsNIsNull() {
            addCriterion("mem_app_nights_n is null");
            return (Criteria) this;
        }

        public Criteria andMemAppNightsNIsNotNull() {
            addCriterion("mem_app_nights_n is not null");
            return (Criteria) this;
        }

        public Criteria andMemAppNightsNEqualTo(Double value) {
            addCriterion("mem_app_nights_n =", value, "memAppNightsN");
            return (Criteria) this;
        }

        public Criteria andMemAppNightsNNotEqualTo(Double value) {
            addCriterion("mem_app_nights_n <>", value, "memAppNightsN");
            return (Criteria) this;
        }

        public Criteria andMemAppNightsNGreaterThan(Double value) {
            addCriterion("mem_app_nights_n >", value, "memAppNightsN");
            return (Criteria) this;
        }

        public Criteria andMemAppNightsNGreaterThanOrEqualTo(Double value) {
            addCriterion("mem_app_nights_n >=", value, "memAppNightsN");
            return (Criteria) this;
        }

        public Criteria andMemAppNightsNLessThan(Double value) {
            addCriterion("mem_app_nights_n <", value, "memAppNightsN");
            return (Criteria) this;
        }

        public Criteria andMemAppNightsNLessThanOrEqualTo(Double value) {
            addCriterion("mem_app_nights_n <=", value, "memAppNightsN");
            return (Criteria) this;
        }

        public Criteria andMemAppNightsNIn(List<Double> values) {
            addCriterion("mem_app_nights_n in", values, "memAppNightsN");
            return (Criteria) this;
        }

        public Criteria andMemAppNightsNNotIn(List<Double> values) {
            addCriterion("mem_app_nights_n not in", values, "memAppNightsN");
            return (Criteria) this;
        }

        public Criteria andMemAppNightsNBetween(Double value1, Double value2) {
            addCriterion("mem_app_nights_n between", value1, value2, "memAppNightsN");
            return (Criteria) this;
        }

        public Criteria andMemAppNightsNNotBetween(Double value1, Double value2) {
            addCriterion("mem_app_nights_n not between", value1, value2, "memAppNightsN");
            return (Criteria) this;
        }

        public Criteria andMemAppRoomAmtIsNull() {
            addCriterion("mem_app_room_amt is null");
            return (Criteria) this;
        }

        public Criteria andMemAppRoomAmtIsNotNull() {
            addCriterion("mem_app_room_amt is not null");
            return (Criteria) this;
        }

        public Criteria andMemAppRoomAmtEqualTo(Double value) {
            addCriterion("mem_app_room_amt =", value, "memAppRoomAmt");
            return (Criteria) this;
        }

        public Criteria andMemAppRoomAmtNotEqualTo(Double value) {
            addCriterion("mem_app_room_amt <>", value, "memAppRoomAmt");
            return (Criteria) this;
        }

        public Criteria andMemAppRoomAmtGreaterThan(Double value) {
            addCriterion("mem_app_room_amt >", value, "memAppRoomAmt");
            return (Criteria) this;
        }

        public Criteria andMemAppRoomAmtGreaterThanOrEqualTo(Double value) {
            addCriterion("mem_app_room_amt >=", value, "memAppRoomAmt");
            return (Criteria) this;
        }

        public Criteria andMemAppRoomAmtLessThan(Double value) {
            addCriterion("mem_app_room_amt <", value, "memAppRoomAmt");
            return (Criteria) this;
        }

        public Criteria andMemAppRoomAmtLessThanOrEqualTo(Double value) {
            addCriterion("mem_app_room_amt <=", value, "memAppRoomAmt");
            return (Criteria) this;
        }

        public Criteria andMemAppRoomAmtIn(List<Double> values) {
            addCriterion("mem_app_room_amt in", values, "memAppRoomAmt");
            return (Criteria) this;
        }

        public Criteria andMemAppRoomAmtNotIn(List<Double> values) {
            addCriterion("mem_app_room_amt not in", values, "memAppRoomAmt");
            return (Criteria) this;
        }

        public Criteria andMemAppRoomAmtBetween(Double value1, Double value2) {
            addCriterion("mem_app_room_amt between", value1, value2, "memAppRoomAmt");
            return (Criteria) this;
        }

        public Criteria andMemAppRoomAmtNotBetween(Double value1, Double value2) {
            addCriterion("mem_app_room_amt not between", value1, value2, "memAppRoomAmt");
            return (Criteria) this;
        }

        public Criteria andMemCardNIsNull() {
            addCriterion("mem_card_n is null");
            return (Criteria) this;
        }

        public Criteria andMemCardNIsNotNull() {
            addCriterion("mem_card_n is not null");
            return (Criteria) this;
        }

        public Criteria andMemCardNEqualTo(Long value) {
            addCriterion("mem_card_n =", value, "memCardN");
            return (Criteria) this;
        }

        public Criteria andMemCardNNotEqualTo(Long value) {
            addCriterion("mem_card_n <>", value, "memCardN");
            return (Criteria) this;
        }

        public Criteria andMemCardNGreaterThan(Long value) {
            addCriterion("mem_card_n >", value, "memCardN");
            return (Criteria) this;
        }

        public Criteria andMemCardNGreaterThanOrEqualTo(Long value) {
            addCriterion("mem_card_n >=", value, "memCardN");
            return (Criteria) this;
        }

        public Criteria andMemCardNLessThan(Long value) {
            addCriterion("mem_card_n <", value, "memCardN");
            return (Criteria) this;
        }

        public Criteria andMemCardNLessThanOrEqualTo(Long value) {
            addCriterion("mem_card_n <=", value, "memCardN");
            return (Criteria) this;
        }

        public Criteria andMemCardNIn(List<Long> values) {
            addCriterion("mem_card_n in", values, "memCardN");
            return (Criteria) this;
        }

        public Criteria andMemCardNNotIn(List<Long> values) {
            addCriterion("mem_card_n not in", values, "memCardN");
            return (Criteria) this;
        }

        public Criteria andMemCardNBetween(Long value1, Long value2) {
            addCriterion("mem_card_n between", value1, value2, "memCardN");
            return (Criteria) this;
        }

        public Criteria andMemCardNNotBetween(Long value1, Long value2) {
            addCriterion("mem_card_n not between", value1, value2, "memCardN");
            return (Criteria) this;
        }

        public Criteria andMemCardBonusAmtIsNull() {
            addCriterion("mem_card_bonus_amt is null");
            return (Criteria) this;
        }

        public Criteria andMemCardBonusAmtIsNotNull() {
            addCriterion("mem_card_bonus_amt is not null");
            return (Criteria) this;
        }

        public Criteria andMemCardBonusAmtEqualTo(Double value) {
            addCriterion("mem_card_bonus_amt =", value, "memCardBonusAmt");
            return (Criteria) this;
        }

        public Criteria andMemCardBonusAmtNotEqualTo(Double value) {
            addCriterion("mem_card_bonus_amt <>", value, "memCardBonusAmt");
            return (Criteria) this;
        }

        public Criteria andMemCardBonusAmtGreaterThan(Double value) {
            addCriterion("mem_card_bonus_amt >", value, "memCardBonusAmt");
            return (Criteria) this;
        }

        public Criteria andMemCardBonusAmtGreaterThanOrEqualTo(Double value) {
            addCriterion("mem_card_bonus_amt >=", value, "memCardBonusAmt");
            return (Criteria) this;
        }

        public Criteria andMemCardBonusAmtLessThan(Double value) {
            addCriterion("mem_card_bonus_amt <", value, "memCardBonusAmt");
            return (Criteria) this;
        }

        public Criteria andMemCardBonusAmtLessThanOrEqualTo(Double value) {
            addCriterion("mem_card_bonus_amt <=", value, "memCardBonusAmt");
            return (Criteria) this;
        }

        public Criteria andMemCardBonusAmtIn(List<Double> values) {
            addCriterion("mem_card_bonus_amt in", values, "memCardBonusAmt");
            return (Criteria) this;
        }

        public Criteria andMemCardBonusAmtNotIn(List<Double> values) {
            addCriterion("mem_card_bonus_amt not in", values, "memCardBonusAmt");
            return (Criteria) this;
        }

        public Criteria andMemCardBonusAmtBetween(Double value1, Double value2) {
            addCriterion("mem_card_bonus_amt between", value1, value2, "memCardBonusAmt");
            return (Criteria) this;
        }

        public Criteria andMemCardBonusAmtNotBetween(Double value1, Double value2) {
            addCriterion("mem_card_bonus_amt not between", value1, value2, "memCardBonusAmt");
            return (Criteria) this;
        }

        public Criteria andAppAddMemNIsNull() {
            addCriterion("app_add_mem_n is null");
            return (Criteria) this;
        }

        public Criteria andAppAddMemNIsNotNull() {
            addCriterion("app_add_mem_n is not null");
            return (Criteria) this;
        }

        public Criteria andAppAddMemNEqualTo(Long value) {
            addCriterion("app_add_mem_n =", value, "appAddMemN");
            return (Criteria) this;
        }

        public Criteria andAppAddMemNNotEqualTo(Long value) {
            addCriterion("app_add_mem_n <>", value, "appAddMemN");
            return (Criteria) this;
        }

        public Criteria andAppAddMemNGreaterThan(Long value) {
            addCriterion("app_add_mem_n >", value, "appAddMemN");
            return (Criteria) this;
        }

        public Criteria andAppAddMemNGreaterThanOrEqualTo(Long value) {
            addCriterion("app_add_mem_n >=", value, "appAddMemN");
            return (Criteria) this;
        }

        public Criteria andAppAddMemNLessThan(Long value) {
            addCriterion("app_add_mem_n <", value, "appAddMemN");
            return (Criteria) this;
        }

        public Criteria andAppAddMemNLessThanOrEqualTo(Long value) {
            addCriterion("app_add_mem_n <=", value, "appAddMemN");
            return (Criteria) this;
        }

        public Criteria andAppAddMemNIn(List<Long> values) {
            addCriterion("app_add_mem_n in", values, "appAddMemN");
            return (Criteria) this;
        }

        public Criteria andAppAddMemNNotIn(List<Long> values) {
            addCriterion("app_add_mem_n not in", values, "appAddMemN");
            return (Criteria) this;
        }

        public Criteria andAppAddMemNBetween(Long value1, Long value2) {
            addCriterion("app_add_mem_n between", value1, value2, "appAddMemN");
            return (Criteria) this;
        }

        public Criteria andAppAddMemNNotBetween(Long value1, Long value2) {
            addCriterion("app_add_mem_n not between", value1, value2, "appAddMemN");
            return (Criteria) this;
        }

        public Criteria andPerfBelongMemNIsNull() {
            addCriterion("perf_belong_mem_n is null");
            return (Criteria) this;
        }

        public Criteria andPerfBelongMemNIsNotNull() {
            addCriterion("perf_belong_mem_n is not null");
            return (Criteria) this;
        }

        public Criteria andPerfBelongMemNEqualTo(Long value) {
            addCriterion("perf_belong_mem_n =", value, "perfBelongMemN");
            return (Criteria) this;
        }

        public Criteria andPerfBelongMemNNotEqualTo(Long value) {
            addCriterion("perf_belong_mem_n <>", value, "perfBelongMemN");
            return (Criteria) this;
        }

        public Criteria andPerfBelongMemNGreaterThan(Long value) {
            addCriterion("perf_belong_mem_n >", value, "perfBelongMemN");
            return (Criteria) this;
        }

        public Criteria andPerfBelongMemNGreaterThanOrEqualTo(Long value) {
            addCriterion("perf_belong_mem_n >=", value, "perfBelongMemN");
            return (Criteria) this;
        }

        public Criteria andPerfBelongMemNLessThan(Long value) {
            addCriterion("perf_belong_mem_n <", value, "perfBelongMemN");
            return (Criteria) this;
        }

        public Criteria andPerfBelongMemNLessThanOrEqualTo(Long value) {
            addCriterion("perf_belong_mem_n <=", value, "perfBelongMemN");
            return (Criteria) this;
        }

        public Criteria andPerfBelongMemNIn(List<Long> values) {
            addCriterion("perf_belong_mem_n in", values, "perfBelongMemN");
            return (Criteria) this;
        }

        public Criteria andPerfBelongMemNNotIn(List<Long> values) {
            addCriterion("perf_belong_mem_n not in", values, "perfBelongMemN");
            return (Criteria) this;
        }

        public Criteria andPerfBelongMemNBetween(Long value1, Long value2) {
            addCriterion("perf_belong_mem_n between", value1, value2, "perfBelongMemN");
            return (Criteria) this;
        }

        public Criteria andPerfBelongMemNNotBetween(Long value1, Long value2) {
            addCriterion("perf_belong_mem_n not between", value1, value2, "perfBelongMemN");
            return (Criteria) this;
        }

        public Criteria andPerfLossMemNIsNull() {
            addCriterion("perf_loss_mem_n is null");
            return (Criteria) this;
        }

        public Criteria andPerfLossMemNIsNotNull() {
            addCriterion("perf_loss_mem_n is not null");
            return (Criteria) this;
        }

        public Criteria andPerfLossMemNEqualTo(Long value) {
            addCriterion("perf_loss_mem_n =", value, "perfLossMemN");
            return (Criteria) this;
        }

        public Criteria andPerfLossMemNNotEqualTo(Long value) {
            addCriterion("perf_loss_mem_n <>", value, "perfLossMemN");
            return (Criteria) this;
        }

        public Criteria andPerfLossMemNGreaterThan(Long value) {
            addCriterion("perf_loss_mem_n >", value, "perfLossMemN");
            return (Criteria) this;
        }

        public Criteria andPerfLossMemNGreaterThanOrEqualTo(Long value) {
            addCriterion("perf_loss_mem_n >=", value, "perfLossMemN");
            return (Criteria) this;
        }

        public Criteria andPerfLossMemNLessThan(Long value) {
            addCriterion("perf_loss_mem_n <", value, "perfLossMemN");
            return (Criteria) this;
        }

        public Criteria andPerfLossMemNLessThanOrEqualTo(Long value) {
            addCriterion("perf_loss_mem_n <=", value, "perfLossMemN");
            return (Criteria) this;
        }

        public Criteria andPerfLossMemNIn(List<Long> values) {
            addCriterion("perf_loss_mem_n in", values, "perfLossMemN");
            return (Criteria) this;
        }

        public Criteria andPerfLossMemNNotIn(List<Long> values) {
            addCriterion("perf_loss_mem_n not in", values, "perfLossMemN");
            return (Criteria) this;
        }

        public Criteria andPerfLossMemNBetween(Long value1, Long value2) {
            addCriterion("perf_loss_mem_n between", value1, value2, "perfLossMemN");
            return (Criteria) this;
        }

        public Criteria andPerfLossMemNNotBetween(Long value1, Long value2) {
            addCriterion("perf_loss_mem_n not between", value1, value2, "perfLossMemN");
            return (Criteria) this;
        }

        public Criteria andCompanyCardNIsNull() {
            addCriterion("company_card_n is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCardNIsNotNull() {
            addCriterion("company_card_n is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCardNEqualTo(Long value) {
            addCriterion("company_card_n =", value, "companyCardN");
            return (Criteria) this;
        }

        public Criteria andCompanyCardNNotEqualTo(Long value) {
            addCriterion("company_card_n <>", value, "companyCardN");
            return (Criteria) this;
        }

        public Criteria andCompanyCardNGreaterThan(Long value) {
            addCriterion("company_card_n >", value, "companyCardN");
            return (Criteria) this;
        }

        public Criteria andCompanyCardNGreaterThanOrEqualTo(Long value) {
            addCriterion("company_card_n >=", value, "companyCardN");
            return (Criteria) this;
        }

        public Criteria andCompanyCardNLessThan(Long value) {
            addCriterion("company_card_n <", value, "companyCardN");
            return (Criteria) this;
        }

        public Criteria andCompanyCardNLessThanOrEqualTo(Long value) {
            addCriterion("company_card_n <=", value, "companyCardN");
            return (Criteria) this;
        }

        public Criteria andCompanyCardNIn(List<Long> values) {
            addCriterion("company_card_n in", values, "companyCardN");
            return (Criteria) this;
        }

        public Criteria andCompanyCardNNotIn(List<Long> values) {
            addCriterion("company_card_n not in", values, "companyCardN");
            return (Criteria) this;
        }

        public Criteria andCompanyCardNBetween(Long value1, Long value2) {
            addCriterion("company_card_n between", value1, value2, "companyCardN");
            return (Criteria) this;
        }

        public Criteria andCompanyCardNNotBetween(Long value1, Long value2) {
            addCriterion("company_card_n not between", value1, value2, "companyCardN");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNIsNull() {
            addCriterion("hotel_capacity_n is null");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNIsNotNull() {
            addCriterion("hotel_capacity_n is not null");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNEqualTo(Long value) {
            addCriterion("hotel_capacity_n =", value, "hotelCapacityN");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNNotEqualTo(Long value) {
            addCriterion("hotel_capacity_n <>", value, "hotelCapacityN");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNGreaterThan(Long value) {
            addCriterion("hotel_capacity_n >", value, "hotelCapacityN");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNGreaterThanOrEqualTo(Long value) {
            addCriterion("hotel_capacity_n >=", value, "hotelCapacityN");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNLessThan(Long value) {
            addCriterion("hotel_capacity_n <", value, "hotelCapacityN");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNLessThanOrEqualTo(Long value) {
            addCriterion("hotel_capacity_n <=", value, "hotelCapacityN");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNIn(List<Long> values) {
            addCriterion("hotel_capacity_n in", values, "hotelCapacityN");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNNotIn(List<Long> values) {
            addCriterion("hotel_capacity_n not in", values, "hotelCapacityN");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNBetween(Long value1, Long value2) {
            addCriterion("hotel_capacity_n between", value1, value2, "hotelCapacityN");
            return (Criteria) this;
        }

        public Criteria andHotelCapacityNNotBetween(Long value1, Long value2) {
            addCriterion("hotel_capacity_n not between", value1, value2, "hotelCapacityN");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andHotelCodeLikeInsensitive(String value) {
            addCriterion("upper(hotel_code) like", value.toUpperCase(), "hotelCode");
            return (Criteria) this;
        }

        public Criteria andEmployeeNcIdLikeInsensitive(String value) {
            addCriterion("upper(employee_nc_id) like", value.toUpperCase(), "employeeNcId");
            return (Criteria) this;
        }

        public Criteria andHotelNameLikeInsensitive(String value) {
            addCriterion("upper(hotel_name) like", value.toUpperCase(), "hotelName");
            return (Criteria) this;
        }

        public Criteria andEmployeeNameLikeInsensitive(String value) {
            addCriterion("upper(employee_name) like", value.toUpperCase(), "employeeName");
            return (Criteria) this;
        }

        public Criteria andEmployeeDeptLikeInsensitive(String value) {
            addCriterion("upper(employee_dept) like", value.toUpperCase(), "employeeDept");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}