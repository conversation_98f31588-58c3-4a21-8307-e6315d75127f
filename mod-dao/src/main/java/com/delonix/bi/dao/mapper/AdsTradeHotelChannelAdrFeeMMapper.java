package com.delonix.bi.dao.mapper;

import com.delonix.bi.dao.model.AdsTradeHotelChannelAdrFeeM;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业主账单渠道房型ADR与佣金表 Mapper
 */
public interface AdsTradeHotelChannelAdrFeeMMapper {

    /**
     * 根据酒店代码和年月查询渠道房型收益数据
     */
    List<AdsTradeHotelChannelAdrFeeM> selectByHotelCodeAndYearMonth(@Param("hotelCode") String hotelCode, 
                                                                   @Param("yearMonth") String yearMonth);
} 