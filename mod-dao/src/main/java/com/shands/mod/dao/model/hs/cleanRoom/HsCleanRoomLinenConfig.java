package com.shands.mod.dao.model.hs.cleanRoom;

import java.util.Date;

public class HsCleanRoomLinenConfig {

  private Integer id;

  private String linenItem;

  private Integer specifyRoom;

  private Integer num;

  private Integer sort;

  private Integer companyId;

  private Integer deleted;

  private Date createTime;

  private Integer createUser;

  private Date updateTime;

  private Integer updateUser;

  private String roomType;

  public Integer getNum() {
    return num;
  }

  public void setNum(Integer num) {
    this.num = num;
  }

  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public String getLinenItem() {
    return linenItem;
  }

  public void setLinenItem(String linenItem) {
    this.linenItem = linenItem == null ? null : linenItem.trim();
  }

  public Integer getSpecifyRoom() {
    return specifyRoom;
  }

  public void setSpecifyRoom(Integer specifyRoom) {
    this.specifyRoom = specifyRoom;
  }

  public Integer getSort() {
    return sort;
  }

  public void setSort(Integer sort) {
    this.sort = sort;
  }

  public Integer getCompanyId() {
    return companyId;
  }

  public void setCompanyId(Integer companyId) {
    this.companyId = companyId;
  }

  public Integer getDeleted() {
    return deleted;
  }

  public void setDeleted(Integer deleted) {
    this.deleted = deleted;
  }

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public Integer getCreateUser() {
    return createUser;
  }

  public void setCreateUser(Integer createUser) {
    this.createUser = createUser;
  }

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public Integer getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(Integer updateUser) {
    this.updateUser = updateUser;
  }

  public String getRoomType() {
    return roomType;
  }

  public void setRoomType(String roomType) {
    this.roomType = roomType == null ? null : roomType.trim();
  }
}