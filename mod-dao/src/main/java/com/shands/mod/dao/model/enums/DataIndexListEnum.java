package com.shands.mod.dao.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据看板
 */
@Getter
@AllArgsConstructor
public enum DataIndexListEnum {

  blocGrossRevenue("totalAmt","ads"),
  blocRoomRevenue("roomAmt","ads"),
  blocCateringRevenue("cateringAmt","ads"),
  blocOtherRevenue("otherAmt","ads"),
  hotelGrossRevenue("totalAmt","ads"),
  hotelRoomRevenue("roomAmt","ads"),
  hotelCateringRevenue("cateringAmt","ads"),
  hotelOtherRevenue("otherAmt","ads"),
  blocGrossRevenueBudget("totalAmtFinishRate","finishRate"),
  blocRoomRevenueBudget("roomAmtFinishRate","finishRate"),
  blocCateringRevenueBudget("cateringAmtFinishRate","finishRate"),
  hotelGrossRevenueBudget("totalAmtFinishRate","finishRate"),
  hotelRoomRevenueBudget("roomAmtFinishRate","finishRate"),
  hotelCateringRevenueBudget("cateringAmtFinishRate","finishRate"),
  blocInvestOCC("investOCC","ads"),
  blocOperationOCC("dayOCC","ads"),
  blocInvestADR("investARD","ads"),
  blocOperationADR("dayADR","ads"),
  blocInvestRevPAR("investRevParMetrics","ads"),
  blocOperationRevPAR("dayRevPAR","ads"),
  hotelInvestOCC("investOCC","ads"),
  hotelOperationOCC("dayOCC","ads"),
  hotelInvestADR("investARD","ads"),
  hotelOperationADR("dayADR","ads"),
  hotelInvestRevPAR("investRevParMetrics","ads"),
  hotelOperationRevPAR("dayRevPAR","ads"),
  blocFaceToFace("memberF2F","ads"),
  blocFaceToFaceRate("faceToFaceRate","ads"),
  blocCorporateMember("entMemberNum","ads"),
  blocMemberNightContributeRate("memberNightContributeRate", "ads"),
  blocMemberRoomRevenue("memberRoomRevenue", "ads"),
  blocAppDownloads("appDownloadN","ads"),
  blocGiftBagSales("giftSale","ads"),
  blocRoomNightNum("channelWebN","ads"),
  blocRoomNightLeaseRate("roomNightLeaseRate","ads"),
  blocRoomNightVolumeRate("roomNightVolumeRate","ads"),
  blocBdxRoomNightNum("bdxRoom","ads"),
  blocBdxRoomNightLeaseRate("bdxRoomNightLeaseRate","ads"),
  blocBdxRoomNightVolumeRate("bdxRoomNightVolumeRate","ads"),
  blocSqRoomNightNum("sqRoom","ads"),
  blocSqRoomNightLeaseRate("sqRoomNightLeaseRate","ads"),
  blocSqRoomNightVolumeRate("sqRoomNightVolumeRate","ads"),
  blocIncentiveOrder("excitationOrderRoom","ads"),
  blocMotivateOther("excitationOtherOrderRoom","ads"),
  blocMotivateOtherRate("motivateOtherRate","ads"),
  blocActivate("hotelActiveEmpN","ads"),
  blocActivationRate("activeRate","ads"),
  hotelFaceToFace("memberF2F","ads"),
  hotelFaceToFaceRate("faceToFaceRate","ads"),
  hotelMemberNightContributeRate("memberNightContributeRate", "ads"),
  hotelMemberRoomRevenue("memberRoomRevenue", "ads"),
  hotelCorporateMember("entMemberNum","ads"),
  hotelAppDownloads("appDownloadN","ads"),
  hotelGiftBagSales("giftSale","ads"),
  hotelRoomNightNum("channelWebN","ads"),
  hotelRoomNightLeaseRate("roomNightLeaseRate","ads"),
  hotelRoomNightVolumeRate("roomNightVolumeRate","ads"),
  hotelBdxRoomNightNum("bdxRoom","ads"),
  hotelBdxRoomNightLeaseRate("bdxRoomNightLeaseRate","ads"),
  hotelBdxRoomNightVolumeRate("bdxRoomNightVolumeRate","ads"),
  hotelSqRoomNightNum("sqRoom","ads"),
  hotelSqRoomNightLeaseRate("sqRoomNightLeaseRate","ads"),
  hotelSqRoomNightVolumeRate("sqRoomNightVolumeRate","ads"),
  hotelIncentiveOrder("excitationOrderRoom","ads"),
  hotelMotivateOther("excitationOtherOrderRoom","ads"),
  hotelMotivateOtherRate("motivateOtherRate","ads"),
  hotelActivate("hotelActiveEmpN","ads"),
  hotelActivationRate("activeRate","ads"),
  blocMember("memDevelopeN","ads"),
  blocConsumePeo("memConsumeAmt","ads"),
  blocConsumePeoFirst("firstPurchasePeo","ads"),
  blocConsumePeoAfter("repurchasePeo","ads"),
  blocMagicWeapon("magicWeapon","ads"),
  hotelMember("memDevelopeN","ads"),
  hotelConsumePeo("memConsumeAmt","ads"),
  hotelConsumePeoFirst("firstPurchasePeo","ads"),
  hotelConsumePeoAfter("repurchasePeo","ads"),
  hotelMagicWeapon("magicWeapon","ads"),
  blocAdditionalCommentAverageScore("currdayScoreN","comment"),
  blocAdditionalCommentSingleRoom("newRoomOnlineReview","add"),
  blocAdditionalCommentVolume("currdayCommentN","comment"),
  // 新增意见量
  blocNewOpinionNum("currdayOpinionN","comment"),
  // 新增点评意见量
  blocNewCommentOpinionNum("currdayCommentOpinionN","comment"),
  blocBdxPagePoints("bdx","comment"),
  blocSqPagePoints("gw","comment"),
  blocXcPagePoints("xieCheng","comment"),
  blocMtPagePoints("meiTuan","comment"),
  hotelAdditionalCommentAverageScore("currdayScoreN","comment"),
  hotelAdditionalCommentSingleRoom("newRoomOnlineReview","add"),
  hotelAdditionalCommentVolume("currdayCommentN","comment"),
  // 酒店新增意见量
  hotelNewOpinionNum("currdayOpinionN","comment"),
  // 酒店新增点评意见量
  hotelNewCommentOpinionNum("currdayCommentOpinionN","comment"),
  hotelBdxPagePoints("bdx","comment"),
  hotelSqPagePoints("gw","comment"),
  hotelXcPagePoints("xieCheng","comment"),
  hotelMtPagePoints("meiTuan","comment"),
  hotelSupplyChainMoney("supplyChainMoney","rate"),
  hotelUniformRecoveryRate("uniformRecoveryRate","rate"),
  blocSupplyChainMoney("supplyChainMoney","rate"),
  blocUniformRecoveryRate("uniformRecoveryRate","rate"),

  blocGWRoomRevenue("gwRoomRevenue","ads"),
  hotelGWRoomRevenue("gwRoomRevenue","ads"),
  hotelCorporateMemberNum("enterpriseMember","ads"),

  blocOTAOnlineRoomNightNum("otaOnlineRoomNight","ads"),
  hotelOTAOnlineRoomNightNum("otaOnlineRoomNight","ads"),

  blocOTAOnlineRate("otaOnlineRate","ads"),
  hotelOTAOnlineRate("otaOnlineRate","ads"),
  blocCtripOnlineRate("ctripOnlineRate","ads"),
  hotelCtripOnlineRate("ctripOnlineRate","ads"),
  blocMeituanOnlineRate("meituanOnlineRate","ads"),
  hotelMeituanOnlineRate("meituanOnlineRate","ads"),
  blocTaobaoOnlineRate("feizhuOnlineRate","ads"),
  hotelTaobaoOnlineRate("feizhuOnlineRate","ads"),
  blocOtherOTAOnlineRate("otherOnlineRate","ads"),
  hotelOtherOTAOnlineRate("otherOnlineRate","ads"),


  // 数据看板-会员数据
  blocMemberScale("totalMemDevelopeN","md"),
  blocMagicWriteOff("magicWriteOff","md"),
  blocConsumerMemberAddScale("consumerNewMember","md"),
  blocConsumerMemberScale("consumerMember","md"),
  blocProduceTimeValue("produceTimeValue","md"),
  blocConsumeTimeValue("consumeTimeValue","md"),
  blocBdxCheckInNum("bdxCheckInNum","md"),
  blocMemberCheckInAsCustomerNum("memberCheckInAsCustomerNum","md"),
  blocCheckInWithMatchedMagicMemberNum("checkInWithMatchedMagicMemberNum","md"),
  blocCheckInWithMatchedMagicNum("checkInWithMatchedMagicNum","md"),
  blocMagicWeaponUsageMemberNum("magicWeaponUsageMemberNum","md"),
  hotelConsumeTimeValue("consumeTimeValue","md"),
  hotelProduceTimeValue("produceTimeValue","md"),
  hotelConsumerMemberScale("consumerMember","md"),
  hotelConsumerMemberAddScale("consumerNewMember","md"),
  hotelMemberScale("totalMemDevelopeN","md"),
  hotelMagicWriteOff("magicWriteOff","md"),
  hotelBdxCheckInNum("bdxCheckInNum","md"),
  hotelMemberCheckInAsCustomerNum("memberCheckInAsCustomerNum","md"),
  hotelCheckInWithMatchedMagicMemberNum("checkInWithMatchedMagicMemberNum","md"),
  hotelCheckInWithMatchedMagicNum("checkInWithMatchedMagicNum","md"),
  hotelMagicWeaponUsageMemberNum("magicWeaponUsageMemberNum","md"),
  ;

  //数据code
  private String dataCode;
  private String dataType;


  public static String getCode(String name) {
    for (DataIndexListEnum b : DataIndexListEnum.values()) {
      if (name.equals(b.name())) {
        return b.getDataCode();
      }
    }
    return null;
  }

  public static String getType(String name) {
    for (DataIndexListEnum b : DataIndexListEnum.values()) {
      if (name.equals(b.name())) {
        return b.getDataType();
      }
    }
    return null;
  }


}