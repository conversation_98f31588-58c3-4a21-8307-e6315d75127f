package com.shands.mod.dao.model.mp.bo;

import com.shands.mod.dao.model.workorder.bo.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 工单配置字段详情表(MpAssessTask)实体类
 *
 * <AUTHOR>
 * @since 2022-11-02 18:14:05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@ApiModel("方案添加参数")
public class MpAssessTaskAddBo extends Page {

  @ApiModelProperty("code")
  private String taskCode;

  /**
   * 名称
   */
  @ApiModelProperty("名称")
  private String name;

  @ApiModelProperty("描述")
  private String remark;
  /**
   * 考核时间类型
   */
  @ApiModelProperty("考核时间类型")
  private String timeType;

  @ApiModelProperty("考核月份")
  private Integer month;
  /**
   * 开始时间
   */
  @ApiModelProperty("开始时间")
  private Date startDate;
  /**
   * 结束时间
   */
  @ApiModelProperty("结束时间")
  private Date endDate;

  @ApiModelProperty("事业code")
  private List<String> deptList;

  @ApiModelProperty("酒店状态")
  private String hotelStatus;

  @ApiModelProperty("管理方式")
  private List<String> cooperationList;

  private List<MpAssessTaskTragetBo> tragetList;

  private String batchCode;
}

