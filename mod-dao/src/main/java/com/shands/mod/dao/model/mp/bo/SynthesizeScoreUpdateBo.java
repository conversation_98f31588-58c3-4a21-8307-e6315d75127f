package com.shands.mod.dao.model.mp.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 价格倒挂记录(MpPriceInversion)实体类
 *
 * <AUTHOR>
 * @since 2022-11-16 10:16:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
public class SynthesizeScoreUpdateBo implements Serializable {

  @ApiModelProperty("同期方案标识")
  private String batchCode;

  @ApiModelProperty("方案code")
  private List<String> taskCodeList;

  @ApiModelProperty("总经理id")
  private Integer userId;

  @ApiModelProperty("原始综合分")
  private Double originalScore;

  @ApiModelProperty("修改综合分")
  private Double modifyScore;

  @ApiModelProperty("说明")
  private String remark;

  @ApiModelProperty("图片")
  private String picture;
}

