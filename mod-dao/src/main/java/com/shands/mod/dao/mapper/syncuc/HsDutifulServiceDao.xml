<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.syncuc.HsDutifulServiceDao">

  <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.syncuc.HsDutifulService">
    <!--@Table hs_dutiful_service-->
    <result property="id" column="id" jdbcType="INTEGER"/>
    <result property="serviceId" column="service_id" jdbcType="INTEGER"/>
    <result property="serviceType" column="service_type" jdbcType="INTEGER"/>
    <result property="serviceType" column="service_type" jdbcType="VARCHAR"/>
    <result property="userId" column="user_id" jdbcType="INTEGER"/>
    <result property="hotelId" column="hotel_id" jdbcType="INTEGER"/>
    <result property="status" column="status" jdbcType="INTEGER"/>
    <result property="createUser" column="create_user" jdbcType="INTEGER"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
    <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
  </resultMap>

  <!--查询单个-->
  <select id="queryById" resultMap="BaseResultMap">
    select id,
           service_id,
           service_type,
           service_name,
           user_id,
           hotel_id,
           status,
           create_user,
           create_time,
           update_user,
           update_time
    from hs_dutiful_service
    where id = #{id}
  </select>

  <!--查询指定行数据-->
  <select id="queryAllByLimit" resultMap="BaseResultMap">
    select id,
           service_id,
           service_type,
           user_id,
           hotel_id,
           status,
           create_user,
           create_time,
           update_user,
           update_time
    from hs_dutiful_service limit #{offset}, #{limit}
  </select>

  <!--通过实体作为筛选条件查询-->
  <select id="queryAll" resultMap="BaseResultMap">
    select
    id, service_id, service_type, user_id, hotel_id, status, create_user, create_time, update_user,
    update_time
    from hs_dutiful_service
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="serviceId != null">
        and service_id = #{serviceId}
      </if>
      <if test="serviceType != null">
        and service_type = #{serviceType}
      </if>
      <if test="userId != null">
        and user_id = #{userId}
      </if>
      <if test="hotelId != null">
        and hotel_id = #{hotelId}
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="createUser != null">
        and create_user = #{createUser}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime}
      </if>
      <if test="updateUser != null">
        and update_user = #{updateUser}
      </if>
      <if test="updateTime != null">
        and update_time = #{updateTime}
      </if>
    </where>
  </select>

  <!--新增所有列-->
  <insert id="insert" keyProperty="id" useGeneratedKeys="true">
    insert into hs_dutiful_service(service_id, service_type, user_id, hotel_id, status,
                                              create_user, create_time, update_user, update_time)
    values (#{serviceId}, #{serviceType}, #{userId}, #{hotelId}, #{status}, #{createUser},
            #{createTime}, #{updateUser}, #{updateTime})
  </insert>

  <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
    insert into hs_dutiful_service(service_id, service_name, service_type, user_id, hotel_id, status,
    create_user, create_time, update_user, update_time)
    values
    <foreach collection="entities" item="entity" separator=",">
      (#{entity.serviceId}, #{entity.serviceName}, #{entity.serviceType}, #{entity.userId},
       #{entity.hotelId}, #{entity.status}, #{entity.createUser}, #{entity.createTime},
      #{entity.updateUser}, #{entity.updateTime})
    </foreach>
  </insert>

  <!--通过主键修改数据-->
  <update id="update">
    update hs_dutiful_service
    <set>
      <if test="serviceId != null">
        service_id = #{serviceId},
      </if>
      <if test="serviceType != null">
        service_type = #{serviceType},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="hotelId != null">
        hotel_id = #{hotelId},
      </if>
      <if test="status != null">
        status = #{status},
      </if>
      <if test="createUser != null">
        create_user = #{createUser},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
    </set>
    where id = #{id}
  </update>

  <!--通过主键删除-->
  <delete id="deleteById">
    delete
    from hs_dutiful_service
    where id = #{id}
  </delete>

</mapper>