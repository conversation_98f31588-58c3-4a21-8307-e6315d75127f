package com.shands.mod.dao.model.workorder.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import java.util.Date;
import java.io.Serializable;

/**
 * 工单流转表(NwWorkOrderTransfer)实体类
 *
 * <AUTHOR>
 * @since 2022-05-05 15:56:28
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Data
public class NwWorkOrderTransfer implements Serializable {

  private static final long serialVersionUID = 404108923612308011L;

  private Integer id;
  /**
   * 版本号 版本号
   */
  private Integer version;
  /**
   * 创建时间
   */
  private Date createTime;
  /**
   * 创建人
   */
  private Integer createUser;
  /**
   * 修改时间
   */
  private Date updateTime;
  /**
   * 修改人
   */
  private Integer updateUser;
  /**
   * 删除标识
   */
  private Integer deleted;

  private Integer workOrderId;

  private String workOrderStatus;

  private Integer beforeUser;

  private Integer receiveUser;

  private String remark;

  private String opration;

  private String createUserName;

  private String url;

  private String file;


}

