package com.shands.mod.dao.model.syncuc;

import java.io.Serializable;
import java.util.Date;
import com.shands.mod.dao.model.enums.BrandEnum;
import com.shands.mod.dao.model.enums.HotelStatusEnum;
import com.shands.mod.dao.model.enums.OwnershipEnum;
import lombok.Data;

/**
 * mod_hotel_info
 *
 * <AUTHOR>
@Data
public class ModHotelInfo implements Serializable {

  /**
   * 酒店编号
   */
  private Integer hotelId;

  /**
   * 酒店代码
   */
  private String hotelCode;

  /**
   * 酒店名称
   */
  private String hotelName;

  /**
   * 酒店集团编码
   */
  private String hotelGroupId;

  /**
   * 酒店品牌代码
   */
  private String hotelBrandCode;

  /**
   * 酒店品牌名称
   */
  private String hotelBrandName;

  /**
   * 酒店事业部代码
   */
  private String hotelOwnershipCode;

  /**
   * 酒店事业部名称
   */
  private String hotelOwnershipName;

  /**
   * 酒店管理方式代码
   */
  private String hotelCooperationCode;

  /**
   * 酒店地址信息
   */
  private String hotelAddress;

  /**
   * 通宝酒店编号
   */
  private Integer ucCompanyId;

  /**
   * 行业/产业 管理公司|酒店
   */
  private String trade;

  /**
   * 酒店状态 1：可用  0：不可用
   */
  private Integer hotelStatus;

  /**
   * 小程序展示状态 1：展示 0：不展示
   */
  private Integer miniprogramStatus;

  /**
   * 位置校验状态 1：开启 0：未开启
   */
  private Integer siteStatus;

  /**
   * 入住短信发送开启状态 1：开启 0：未开启
   */
  private Integer inSmsStatus;

  /**
   * 领券中心展示状态 1：展示 0：不展示
   */
  private Integer couponStatus;

  /**
   * 范围半径设置
   */
  private Integer radius;

  /**
   * 腾讯地图：经度信息
   */
  private String lngGd;

  /**
   * 腾讯地图：维度信息
   */
  private String latGd;

  /**
   * 酒店介绍图片
   */
  private String pDescUrl;

  /**
   * 酒店图标
   */
  private String pLogoUrl;

  /**
   * 酒店banner图标
   */
  private String pBannerUrl;

  /**
   * 二维码生产背景图片
   */
  private String pQrCodeUrl;

  /**
   * 排序
   */
  private Integer sort;

  /**
   * 墨迹天气城市id
   */
  private String weatherCityId;

  /**
   * 会员渠道标识
   */
  private String mMemberChannel;

  /**
   * 会员注册卡类型((卡类型 曼居卡类型：MJ  注册曼居卡级别：MC  商祺卡 BC，注册商祺普卡BE))
   */
  private String mCardType;

  /**
   * 会员注册卡级别
   */
  private String mCardLevel;

  /**
   * 支付渠道标识
   */
  private String applyChannel;

  /**
   * 腾讯地图图层id
   */
  private String mapCoverage;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 修改时间
   */
  private Date updateTime;

  /**
   * 备注信息
   */
  private String reamrk;

  /**
   * 酒店服务电话 hotel_phone
   */
  private String hotelPhone;

  /**
   * 绿云api地址 api_url
   */
  private String apiUrl;

  /**
   * 绿云酒店ID
   */
  private Integer pmsHotelId;

  /**
   * pos请求url地址
   */
  private String posUrl;

  /**
   * 会员充值开关 1：开 0：关
   */
  private Integer rechargeFlag;

  /**
   * 会员发展类型 BDW：百达星系 KAIYUAN：商祺会
   */
  private String registerType;

  /**
   * pms类型 IGROUP：新pms GREEN：绿云
   */
  private String pmsFlag;

  /**
   * 酒店筹建状态
   */
  private String contract;

  private String contractName;

  /**
   * 明星店标识 1：明星店 0：普通店
   */
  private Integer starFlag;

  private Integer grade;

  private String cityId;

  private String address;

  private static final long serialVersionUID = 1L;

  public String getContractName() {
    return HotelStatusEnum.getValue(contract);
  }

  public void setContractName(String contractName) {
    this.contractName = contractName;
  }

  public String getHotelBrandName() {
    return BrandEnum.getValue(hotelBrandCode);
  }

  public void setHotelBrandName(String hotelBrandName) {
    this.hotelBrandName = hotelBrandName;
  }

  public String getHotelOwnershipName() {
    return OwnershipEnum.getValue(hotelOwnershipCode);
  }

  public void setHotelOwnershipName(String hotelOwnershipName) {
    this.hotelOwnershipName = hotelOwnershipName;
  }
}