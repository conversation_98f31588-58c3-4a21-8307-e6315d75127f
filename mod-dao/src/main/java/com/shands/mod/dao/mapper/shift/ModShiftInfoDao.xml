<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.shift.ModShiftInfoDao">

  <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.shift.ModShiftInfo">
    <!--@Table mod_shift_info-->
    <result property="id" column="id" jdbcType="INTEGER"/>
    <result property="hotelId" column="hotel_id" jdbcType="INTEGER"/>
    <result property="shiftType" column="shift_type" jdbcType="VARCHAR"/>
    <result property="shiftName" column="shift_name" jdbcType="VARCHAR"/>
    <result property="userIds" column="user_ids" jdbcType="VARCHAR"/>
    <result property="beginTime" column="begin_time" jdbcType="VARCHAR"/>
    <result property="endTime" column="end_time" jdbcType="VARCHAR"/>
    <result property="nextDay" column="next_day" jdbcType="INTEGER"/>
    <result property="status" column="status" jdbcType="INTEGER"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="createUser" column="create_user" jdbcType="INTEGER"/>
    <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
    <result property="remark" column="remark" jdbcType="VARCHAR"/>
  </resultMap>

  <!--查询单个-->
  <select id="queryById" resultMap="BaseResultMap">
    select id,
           hotel_id,
           shift_type,
           shift_name,
           user_ids,
           begin_time,
           end_time,
           next_day,
           status,
           create_time,
           create_user,
           update_time,
           update_user,
           remark
    from mod_shift_info
    where id = #{id}
  </select>

  <select id="queryByHotelIdAndShiftType" resultMap="BaseResultMap">
    select
    id, hotel_id, shift_type, shift_name, user_ids, begin_time, end_time, next_day, status, create_time,
    create_user, update_time, update_user, remark
    from mod_shift_info where hotel_id = #{hotelId} and shift_type = #{shiftType} and status = 1
  </select>

  <select id="qurMbServices" resultType="com.shands.mod.dao.model.shift.QurMbServicesVo">
    SELECT hhs.service_type as serviceType, hhs.cnname as cnname, hhs.id as serviceId, ms.COLOUR_PIC as iconUrl,ms.service_extend_type as serviceExtendType
    FROM hs_hotel_service hhs left join mod_service ms on ms.ID = hhs.mod_service_id
    WHERE hhs.COMPANY_ID = #{hotelId}
      AND hhs.status = 1
      AND hhs.deleted = 0
  </select>

  <!--查询指定行数据-->
  <select id="queryAllByLimit" resultMap="BaseResultMap">
    select
    id, hotel_id, shift_type, shift_name, begin_time, end_time, next_day, status, create_time,
    create_user, update_time, update_user, remark
    from mod_shift_info
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="hotelId != null">
        and hotel_id = #{hotelId}
      </if>
      <if test="shiftType != null and shiftType != ''">
        and shift_type = #{shiftType}
      </if>
      <if test="shiftName != null and shiftName != ''">
        and shift_name = #{shiftName}
      </if>
      <if test="beginTime != null">
        and begin_time = #{beginTime}
      </if>
      <if test="endTime != null">
        and end_time = #{endTime}
      </if>
      <if test="nextDay != null">
        and next_day = #{nextDay}
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime}
      </if>
      <if test="createUser != null">
        and create_user = #{createUser}
      </if>
      <if test="updateTime != null">
        and update_time = #{updateTime}
      </if>
      <if test="updateUser != null">
        and update_user = #{updateUser}
      </if>
      <if test="remark != null and remark != ''">
        and remark = #{remark}
      </if>
    </where>
    limit #{pageable.offset}, #{pageable.pageSize}
  </select>

  <!--统计总行数-->
  <select id="count" resultType="java.lang.Long">
    select count(1)
    from mod_shift_info
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="hotelId != null">
        and hotel_id = #{hotelId}
      </if>
      <if test="shiftType != null and shiftType != ''">
        and shift_type = #{shiftType}
      </if>
      <if test="shiftName != null and shiftName != ''">
        and shift_name = #{shiftName}
      </if>
      <if test="beginTime != null">
        and begin_time = #{beginTime}
      </if>
      <if test="endTime != null">
        and end_time = #{endTime}
      </if>
      <if test="nextDay != null">
        and next_day = #{nextDay}
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime}
      </if>
      <if test="createUser != null">
        and create_user = #{createUser}
      </if>
      <if test="updateTime != null">
        and update_time = #{updateTime}
      </if>
      <if test="updateUser != null">
        and update_user = #{updateUser}
      </if>
      <if test="remark != null and remark != ''">
        and remark = #{remark}
      </if>
    </where>
  </select>
  <select id="queryExistShiftByTime" resultType="java.lang.Integer">
    select id from mod_shift_info
    where status = 1
    and shift_type = #{shiftType}
    and hotel_id = #{hotelId}
    and TIME(#{date}) BETWEEN TIME(begin_time) and TIME(end_time)
  </select>

  <select id="queryByHotelIdIgnoreStatus" resultMap="BaseResultMap">
    select id,
           hotel_id,
           shift_type,
           shift_name,
           user_ids,
           begin_time,
           end_time,
           next_day,
           status,
           create_time,
           create_user,
           update_time,
           update_user,
           remark
    from mod_shift_info
    where hotel_id = #{hotelId}
      and shift_type = #{shiftType}
  </select>
  <select id="queryByHotelId" resultMap="BaseResultMap">
    select * from mod_shift_info
    where hotel_id = #{hotelId}
    and status = 1
  </select>
  <select id="queryDisabledShiftId" resultType="java.lang.Integer">
    select id from mod_shift_info
    where hotel_id = #{hotelId}
    and status = 0
    and shift_type = #{shiftType}
  </select>

  <!--新增所有列-->
  <insert id="insert" keyProperty="id" useGeneratedKeys="true">
    insert into mod_shift_info(hotel_id, shift_type, shift_name, user_ids, begin_time, end_time, next_day,
                               status, create_time, create_user, update_time, update_user, remark)
    values (#{hotelId}, #{shiftType}, #{shiftName}, #{userIds}, #{beginTime}, #{endTime}, #{nextDay}, #{status},
            #{createTime}, #{createUser}, #{updateTime}, #{updateUser}, #{remark})
  </insert>

  <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
    insert into mod_shift_info(hotel_id, shift_type, shift_name,user_ids, begin_time, end_time, next_day,
    status, create_time, create_user, update_time, update_user, remark)
    values
    <foreach collection="entities" item="entity" separator=",">
      (#{entity.hotelId}, #{entity.shiftType}, #{entity.shiftName},#{entity.userIds}, #{entity.beginTime},
      #{entity.endTime}, #{entity.nextDay}, #{entity.status}, #{entity.createTime},
      #{entity.createUser}, #{entity.updateTime}, #{entity.updateUser}, #{entity.remark})
    </foreach>
  </insert>

  <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
    insert into mod_shift_info(hotel_id, shift_type, shift_name,user_ids, begin_time, end_time, next_day,
    status, create_time, create_user, update_time, update_user, remark)
    values
    <foreach collection="entities" item="entity" separator=",">
      (#{entity.hotelId}, #{entity.shiftType}, #{entity.shiftName},#{entity.userIds}, #{entity.beginTime},
      #{entity.endTime}, #{entity.nextDay}, #{entity.status}, #{entity.createTime},
      #{entity.createUser}, #{entity.updateTime}, #{entity.updateUser}, #{entity.remark})
    </foreach>
    on duplicate key update
    hotel_id = values(hotel_id),
    shift_type = values(shift_type),
    shift_name = values(shift_name),
    user_ids = values(user_ids),
    begin_time = values(begin_time),
    end_time = values(end_time),
    next_day = values(next_day),
    status = values(status),
    create_time = values(create_time),
    create_user = values(create_user),
    update_time = values(update_time),
    update_user = values(update_user),
    remark = values(remark)
  </insert>

  <!--通过主键修改数据-->
  <update id="update">
    update mod_shift_info
    <set>
      <if test="hotelId != null">
        hotel_id = #{hotelId},
      </if>
      <if test="shiftType != null and shiftType != ''">
        shift_type = #{shiftType},
      </if>
      <if test="shiftName != null and shiftName != ''">
        shift_name = #{shiftName},
      </if>
      <if test="userIds != null and userIds != ''">
        user_ids = #{userIds},
      </if>
      <if test="beginTime != null">
        begin_time = #{beginTime},
      </if>
      <if test="endTime != null">
        end_time = #{endTime},
      </if>
      <if test="nextDay != null">
        next_day = #{nextDay},
      </if>
      <if test="status != null">
        status = #{status},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="createUser != null">
        create_user = #{createUser},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark},
      </if>
    </set>
    where id = #{id}
  </update>

  <update id="updateShiftUserIds">
    update mod_shift_info
    set user_ids = #{userIds}
    where id = #{id}
  </update>

  <!--通过主键删除-->
  <delete id="deleteById">
    delete
    from mod_shift_info
    where id = #{id}
  </delete>


</mapper>

