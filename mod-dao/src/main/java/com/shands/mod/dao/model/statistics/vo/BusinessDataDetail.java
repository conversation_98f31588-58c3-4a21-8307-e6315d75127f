package com.shands.mod.dao.model.statistics.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BusinessDataDetail {

    @ApiModelProperty(value = "本期日期")
    private String currentDate;

    @ApiModelProperty(value = "往期日期")
    private String lastDate;

    @ApiModelProperty(value = "本期值")
    private String currentValue;

    @ApiModelProperty(value = "往期值")
    private String lastValue;

    @ApiModelProperty("增或降（-1 ： 降 0：平 1：增 2：无法比较 ）")
    private Integer trendType;

    @ApiModelProperty("增降幅(-4.01%)")
    private String trendRate;
}
