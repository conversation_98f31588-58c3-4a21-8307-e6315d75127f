<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.training.ModCourseCollectionMapper">

    <resultMap type="com.shands.mod.dao.model.training.po.ModCourseCollection" id="ModCourseCollectionMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="collectionName" column="collection_name" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="onShelfTime" column="on_shelf_time" jdbcType="TIMESTAMP"/>
        <result property="courseGroupId" column="course_group_id" jdbcType="INTEGER"/>
        <result property="labelId" column="label_id" jdbcType="INTEGER"/>
        <result property="courseIntroduction" column="course_introduction" jdbcType="VARCHAR"/>
        <result property="imageUrl" column="image_url" jdbcType="VARCHAR"/>
        <result property="configType" column="config_type" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="ifDelete" column="if_delete" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="brandCode" column="brand_code" jdbcType="VARCHAR"/>
        <result property="brandName" column="brand_name" jdbcType="VARCHAR"/>
        <result property="divisionCode" column="division_code" jdbcType="VARCHAR"/>
        <result property="divisionName" column="division_name" jdbcType="VARCHAR"/>
        <result property="hotelIds" column="hotel_ids" jdbcType="VARCHAR"/>
        <result property="hotelName" column="hotel_name" jdbcType="VARCHAR"/>
        <result property="roleCode" column="role_code" jdbcType="VARCHAR"/>
        <result property="roleName" column="role_name" jdbcType="VARCHAR"/>
        <result property="ifStaff" column="if_staff" jdbcType="INTEGER"/>
        <result property="releaseHotel" column="release_hotel" jdbcType="VARCHAR"/>
        <result property="courseGroupIds" column="course_groupIds" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ModCourseCollectionMap">
        select
        course_groupIds,release_hotel,if_staff,id, collection_name, status, on_shelf_time, course_group_id, label_id, course_introduction, image_url, config_type, remark, if_delete, create_time, update_time, create_user, update_user, version, brand_code, brand_name, division_code, division_name, hotel_ids, hotel_name,role_code,role_name from mod_course_collection
        where id = #{id} and if_delete = 0
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="ModCourseCollectionMap">
        select
          id, collection_name, status, on_shelf_time, course_group_id, label_id, course_introduction, image_url, config_type, remark, if_delete, create_time, update_time, create_user, update_user, version, brand_code, brand_name, division_code, division_name, hotel_ids, hotel_name,role_code,role_name
        from mod_course_collection
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="collectionName != null and collectionName != ''">
                and collection_name = #{collectionName}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="onShelfTime != null">
                and on_shelf_time = #{onShelfTime}
            </if>
            <if test="courseGroupId != null">
                and course_group_id = #{courseGroupId}
            </if>
            <if test="labelId != null">
                and label_id = #{labelId}
            </if>
            <if test="courseIntroduction != null and courseIntroduction != ''">
                and course_introduction = #{courseIntroduction}
            </if>
            <if test="imageUrl != null and imageUrl != ''">
                and image_url = #{imageUrl}
            </if>
            <if test="configType != null and configType != ''">
                and config_type = #{configType}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="ifDelete != null">
                and if_delete = #{ifDelete}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="createUser != null">
                and create_user = #{createUser}
            </if>
            <if test="updateUser != null">
                and update_user = #{updateUser}
            </if>
            <if test="version != null">
                and version = #{version}
            </if>
            <if test="brandCode != null and brandCode != ''">
                and brand_code = #{brandCode}
            </if>
            <if test="brandName != null and brandName != ''">
                and brand_name = #{brandName}
            </if>
            <if test="divisionCode != null and divisionCode != ''">
                and division_code = #{divisionCode}
            </if>
            <if test="divisionName != null and divisionName != ''">
                and division_name = #{divisionName}
            </if>
            <if test="hotelIds != null and hotelIds != ''">
              and hotel_ids = #{hotelIds}
            </if>
            <if test="hotelName != null and hotelName != ''">
              and hotel_name = #{hotelName}
            </if>
            <if test="roleCode != null and roleCode != ''">
              and role_code = #{roleCode}
            </if>
            <if test="roleName != null and roleName != ''">
              and role_name = #{roleName}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from mod_course_collection
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="collectionName != null and collectionName != ''">
                and collection_name = #{collectionName}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="onShelfTime != null">
                and on_shelf_time = #{onShelfTime}
            </if>
            <if test="courseGroupId != null">
                and course_group_id = #{courseGroupId}
            </if>
            <if test="labelId != null">
                and label_id = #{labelId}
            </if>
            <if test="courseIntroduction != null and courseIntroduction != ''">
                and course_introduction = #{courseIntroduction}
            </if>
            <if test="imageUrl != null and imageUrl != ''">
                and image_url = #{imageUrl}
            </if>
            <if test="configType != null and configType != ''">
                and config_type = #{configType}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="ifDelete != null">
                and if_delete = #{ifDelete}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="createUser != null">
                and create_user = #{createUser}
            </if>
            <if test="updateUser != null">
                and update_user = #{updateUser}
            </if>
            <if test="version != null">
                and version = #{version}
            </if>
            <if test="brandCode != null and brandCode != ''">
                and brand_code = #{brandCode}
            </if>
            <if test="brandName != null and brandName != ''">
                and brand_name = #{brandName}
            </if>
            <if test="divisionCode != null and divisionCode != ''">
                and division_code = #{divisionCode}
            </if>
            <if test="divisionName != null and divisionName != ''">
                and division_name = #{divisionName}
            </if>
            <if test="hotelIds != null and hotelIds != ''">
              and hotel_ids = #{hotelIds}
            </if>
            <if test="hotelName != null and hotelName != ''">
              and hotel_name = #{hotelName}
            </if>
            <if test="roleCode != null and roleCode != ''">
              and role_code = #{roleCode}
            </if>
            <if test="roleName != null and roleName != ''">
              and role_name = #{roleName}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into mod_course_collection(course_groupIds,release_hotel,if_staff,collection_name, status, on_shelf_time, course_group_id, label_id, course_introduction, image_url, config_type, remark, if_delete, create_time, update_time, create_user, update_user, version, brand_code, brand_name, division_code, division_name, hotel_ids, hotel_name,role_code,role_name)
        values (#{courseGroupIds},#{releaseHotel},#{ifStaff},#{collectionName}, #{status}, #{onShelfTime}, #{courseGroupId}, #{labelId}, #{courseIntroduction}, #{imageUrl}, #{configType}, #{remark}, #{ifDelete}, #{createTime}, #{updateTime}, #{createUser}, #{updateUser}, #{version}, #{brandCode}, #{brandName}, #{divisionCode}, #{divisionName}, #{hotelIds}, #{hotelName}, #{roleCode}, #{roleName})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into mod_course_collection(if_staff,collection_name, status, on_shelf_time, course_group_id, label_id, course_introduction, image_url, config_type, remark, if_delete, create_time, update_time, create_user, update_user, version, brand_code, brand_name, division_code, division_name, hotel_ids, hotel_name,role_code,role_name)
        values
        <foreach collection="entities" item="entity" separator=",">
        ((#{entity.ifStaff},#{entity.collectionName}, #{entity.status}, #{entity.onShelfTime}, #{entity.courseGroupId}, #{entity.labelId}, #{entity.courseIntroduction}, #{entity.imageUrl}, #{entity.configType}, #{entity.remark}, #{entity.ifDelete}, #{entity.createTime}, #{entity.updateTime}, #{entity.createUser}, #{entity.updateUser}, #{entity.version}, #{entity.brandCode}, #{entity.brandName}, #{entity.divisionCode}, #{entity.divisionName}, #{entity.hotelIds}, #{entity.hotelName}, #{entity.roleCode}, #{entity.roleName})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into mod_course_collection(if_staff,collection_name, status, on_shelf_time, course_group_id, label_id, course_introduction, image_url, config_type, remark, if_delete, create_time, update_time, create_user, update_user, version, brand_code, brand_name, division_code, division_name, hotel_ids, hotel_name,role_code,role_name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.ifStaff},#{entity.collectionName}, #{entity.status}, #{entity.onShelfTime}, #{entity.courseGroupId}, #{entity.labelId}, #{entity.courseIntroduction}, #{entity.imageUrl}, #{entity.configType}, #{entity.remark}, #{entity.ifDelete}, #{entity.createTime}, #{entity.updateTime}, #{entity.createUser}, #{entity.updateUser}, #{entity.version}, #{entity.brandCode}, #{entity.brandName}, #{entity.divisionCode}, #{entity.divisionName}, #{entity.hotelIds}, #{entity.hotelName}, #{entity.roleCode}, #{entity.roleName})
        </foreach>
        on duplicate key update
        if_staff = values(if_staff),
        collection_name = values(collection_name),
        status = values(status),
        on_shelf_time = values(on_shelf_time),
        course_group_id = values(course_group_id),
        label_id = values(label_id),
        course_introduction = values(course_introduction),
        image_url = values(image_url),
        config_type = values(config_type),
        remark = values(remark),
        if_delete = values(if_delete),
        create_time = values(create_time),
        update_time = values(update_time),
        create_user = values(create_user),
        update_user = values(update_user),
        version = values(version),
        brand_code = values(brand_code),
        brand_name = values(brand_name),
        division_code = values(division_code),
        division_name = values(division_name),
        hotel_ids = values(hotel_ids),
        hotel_name = values(hotel_name),
        role_code = values(role_code),
        role_name = values(role_name)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update mod_course_collection
        <set>
          <if test="courseGroupIds != null">
            course_groupIds = #{courseGroupIds},
          </if>
          <if test="releaseHotel != null">
            release_hotel = #{releaseHotel},
          </if>
          <if test="ifStaff != null">
            if_staff = #{ifStaff},
          </if>
            <if test="collectionName != null and collectionName != ''">
                collection_name = #{collectionName},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="onShelfTime != null">
                on_shelf_time = #{onShelfTime},
            </if>
            <if test="courseGroupId != null">
                course_group_id = #{courseGroupId},
            </if>
            <if test="labelId != null">
                label_id = #{labelId},
            </if>
            <if test="courseIntroduction != null and courseIntroduction != ''">
                course_introduction = #{courseIntroduction},
            </if>
            <if test="imageUrl != null and imageUrl != ''">
                image_url = #{imageUrl},
            </if>
            <if test="configType != null and configType != ''">
                config_type = #{configType},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="ifDelete != null">
                if_delete = #{ifDelete},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="createUser != null">
                create_user = #{createUser},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="brandCode != null">
                brand_code = #{brandCode},
            </if>
            <if test="brandName != null">
                brand_name = #{brandName},
            </if>
            <if test="divisionCode != null">
                division_code = #{divisionCode},
            </if>
            <if test="divisionName != null">
                division_name = #{divisionName},
            </if>
            <if test="hotelIds != null">
                hotel_ids = #{hotelIds},
            </if>
            <if test="hotelName != null">
                hotel_name = #{hotelName},
            </if>
            <if test="roleCode != null">
                role_code = #{roleCode},
            </if>
            <if test="roleName != null">
                role_name = #{roleName},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from mod_course_collection where id = #{id}
    </delete>
  <select id="queryCollectionList" resultType="com.shands.mod.dao.model.training.vo.CollectionVo">
      SELECT cc.id as collectionId, cc.collection_name as collectionName, cc.on_shelf_time as shelfTime, cc.`status`,COUNT(mc.id)  as relatedCoursesNum from mod_course_collection cc
      LEFT JOIN mod_course_association ca on  cc.id =ca.course_collect_id and ca.if_delete = 0
      LEFT JOIN mod_course mc on  mc.id =ca.course_id and mc.if_delete = 0
      <where>
        cc.if_delete=0
        <if test="collectionBo.collectionName != null and collectionBo.collectionName != ''">
         and cc.collection_name like CONCAT('%',#{collectionBo.collectionName}, '%')
        </if>
        <if test="collectionBo.status != null">
         and cc.`status` = #{collectionBo.status}
        </if>
        <if test="collectionBo.configType != null and collectionBo.configType != ''">
          and cc.config_type = #{collectionBo.configType}
        </if>
        <if test="collectionBo.hotelId != null and collectionBo.hotelId != ''">
          and cc.hotel_ids LIKE CONCAT('%',#{collectionBo.hotelId},'%')
        </if>
      </where>
    GROUP BY cc.id
    ORDER BY cc.create_time DESC
  </select>

    <select id="selectCourseCollection" resultType="com.shands.mod.dao.model.training.vo.CollectionVo">
      SELECT
        id collectionId,
        collection_name collectionName,
        status status
      FROM
        mod_course_collection
      WHERE if_delete = 0
      <if test="chooseCourseBo.name != null and chooseCourseBo.name != ''">
        AND collection_name LIKE CONCAT('%',#{chooseCourseBo.name},'%')
      </if>
      <if test="chooseCourseBo.configType != null and chooseCourseBo.configType != ''">
        AND config_type = #{chooseCourseBo.configType}
      </if>
      <if test="chooseCourseBo.hotelId != null and chooseCourseBo.hotelId != ''">
        AND hotel_ids LIKE CONCAT('%',#{chooseCourseBo.hotelId},'%')
      </if>
    </select>

  <select id="searchCollection" resultType="com.shands.mod.dao.model.training.vo.LatestCourseVo">

select * from (
    SELECT mc.hotel_ids as hotelIds,mc.brand_code as brandCode,mc.division_code as divisionCode,mc.if_staff as ifStaff,'合集' as `type`, mc.config_type as configType,GROUP_CONCAT(cl.label_name) as labelName ,mc.id as tempId,'COLLECTION' as courseDistinguish, IFNULL(tb1.learningNum,0) as learningNum,mc.id as courseId, mc.collection_name as `name`, mc.image_url as imageUrl from mod_course_collection mc
    LEFT JOIN mod_course_label  cl on FIND_IN_SET(cl.id,mc.label_id)  and cl.label_type='COLLECTION'
    LEFT JOIN (
    SELECT  mc.id as courseId, count(cu.id) as learningNum
    from mod_course_collection mc
    LEFT JOIN mod_course_user cu on cu.course_id= mc.id and cu.type='COLLECTION'
    WHERE mc.if_delete=0
    GROUP BY mc.id
    ) tb1 on tb1.courseId=mc.id
    WHERE
      (mc.config_type= 'BLOC' and mc.`status`=1 and mc.if_delete=0)
     or (mc.hotel_ids like concat('%',#{hotelId},'%') and mc.config_type= 'HOTEL' and mc.if_delete=0 and mc.`status`=1)
    GROUP BY mc.id
) tb2
where  tb2.`name` like concat('%',#{keyWord},'%')
    or tb2.labelName like concat('%',#{keyWord},'%')
    ORDER BY
    CASE WHEN tb2.`name` LIKE concat('%',#{keyWord},'%') THEN 1
    ELSE tb2.labelName  end
  </select>
  <select id="selectCollectionByCourseId"
    resultType="com.shands.mod.dao.model.training.vo.AssociatedCollectionListVo">

    SELECT mc.hotel_ids as hotelIds,mc.brand_code as brandCode,mc.role_code as roleCode,mc.config_type as configType,mc.if_staff as ifStaff,'合集' as `type`,IFNULL(tb1.learningNum,0) as learningNum,mc.collection_name as `name`,mc.image_url as imageUrl,mc.id as tempId from mod_course_collection mc LEFT JOIN mod_course_association  ca on mc.id= ca.course_collect_id
    LEFT JOIN mod_course mcc on mcc.id=ca.course_id
    LEFT JOIN (
    SELECT  mc.id as courseId, count(cu.id) as learningNum
    from mod_course_collection mc
    LEFT JOIN mod_course_user cu on cu.course_id= mc.id and cu.type='COLLECTION'
    WHERE mc.if_delete=0
    GROUP BY mc.id
    ) tb1 on tb1.courseId=mc.id
    WHERE ca.course_id=#{courseId} and mcc.if_delete=0 and mc.if_delete=0 and mc.`status`=1 and ca.if_delete=0
    order by ca.sort asc
  </select>

  <select id="collectionDetails"
    resultType="com.shands.mod.dao.model.training.vo.CollectionDetailsVo">
    SELECT mc.release_hotel as releaseHotel,IFNULL(tb1.learningNum,0)as learningNum ,mc.collection_name as `name`,mc.create_time as createTime,mc.image_url as imageUrl,mc.course_introduction as courseIntroduction,IFNULL(ck.if_keep,0) as if_collect
    from mod_course_collection mc
    LEFT JOIN mod_course_keep ck on ck.course_collect_id=mc.id and ck.course_user_id=#{userId}
    LEFT JOIN (
    SELECT  mc.id as courseId, count(cu.id) as learningNum
    from mod_course_collection mc
    LEFT JOIN mod_course_user cu on cu.course_id= mc.id and cu.type='COLLECTION'
    WHERE mc.if_delete=0 and mc.id=#{collectionId}
    GROUP BY mc.id
    ) tb1 on tb1.courseId=mc.id
    WHERE  mc.id=#{collectionId}  and mc.if_delete=0
  </select>
  <select id="courseCollectionList"
    resultType="com.shands.mod.dao.model.training.vo.LatestCourseVo">

    SELECT mc.hotel_ids as hotelIds,mc.brand_code as brandCode,mc.division_code as divisionCode,mc.if_staff as ifStaff,mc.config_type as configType,ck.create_time as createTime, mc.id as tempId,'COLLECTION' as courseDistinguish,'合集' as `type`,mc.role_code as roleCode,IFNULL(tb1.learningNum,0)as learningNum, mc.id as courseId, mc.hotel_ids, mc.collection_name as `name`,mc.image_url as imageUrl from   mod_course_collection mc
    LEFT JOIN  mod_course_keep ck on mc.id=ck.course_collect_id
  LEFT JOIN (
   SELECT  cu.`type`,mc.id as courseId, count(cu.id) as learningNum
	from mod_course_collection mc
	LEFT JOIN mod_course_user cu on cu.course_id= mc.id and cu.type='COLLECTION'
	WHERE mc.if_delete=0
	GROUP BY mc.id
  ) tb1 on tb1.courseId=mc.id
  WHERE ck.course_user_id=#{userId} and ck.if_keep=1 and mc.if_delete=0 and mc.`status`=1


  </select>

</mapper>

