package com.shands.mod.dao.mapper;

import com.shands.mod.dao.model.datarevision.bo.MemberAnalysisBo;
import com.shands.mod.dao.model.datarevision.vo.GiftSalesVo;
import com.shands.mod.dao.model.datarevision.vo.GiftPackageVo;
import com.shands.mod.dao.model.datarevision.vo.PackageRanKVo;
import com.shands.mod.dao.model.req.elsreport.GiftRankReq;
import com.shands.mod.dao.model.req.elsreport.GiftSalesReq;
import com.shands.mod.dao.model.res.elsreport.GiftRankRes;
import com.shands.mod.dao.model.res.elsreport.GiftSalesRes;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * (DwsTradeGiftDptIncDay)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-01-13 16:42:30
 */
public interface DwsTradeGiftDptIncDayMapper {

  List<GiftRankRes> selectListByData(
    @Param("giftRankReq") GiftRankReq giftRankReq);

  /**
   * 各部门礼包销售礼包数据
   * @param giftSalesReq
   * @return
   */
  List<GiftSalesRes> selectGiftSalesList( @Param("giftSalesReq") GiftSalesReq giftSalesReq);

  /**
   * 根据部门名称查询部门人员销售礼包数据
   * @param giftSalesReq
   * @return
   */
  List<GiftSalesRes> selectGiftSalesListByDep(@Param("giftSalesReq") GiftSalesReq giftSalesReq);

  /**
   * 礼包销售量分布
   * @param memberAnalysisBo
   * @return
   */
  List<GiftPackageVo> findGiftSalesDistribute(@Param("memberAnalysisBo") MemberAnalysisBo memberAnalysisBo);

  /**
   * 礼包销售量趋势
   * @param memberAnalysisBo
   * @return
   */
  List<GiftSalesVo> findGiftSaleCounts(@Param("giftNames") List<String> giftNames,@Param("memberAnalysisBo") MemberAnalysisBo memberAnalysisBo);

  String findMaxDate();
}

