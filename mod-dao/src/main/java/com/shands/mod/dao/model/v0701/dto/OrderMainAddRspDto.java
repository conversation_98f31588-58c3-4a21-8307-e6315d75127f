package com.shands.mod.dao.model.v0701.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 百达屋创建订单返回结果实体类
 * @Author: guixuehai
 * @Date: 2024/8/16 11:15
 */
@Data
public class OrderMainAddRspDto {

  @ApiModelProperty(value = "订单Id")
  private String orderMainId; //orderMainNo
  @ApiModelProperty(value = "提示")
  private String msg;
  @ApiModelProperty(value = "是否需要拉起支付")
  private Boolean needPay;
  @ApiModelProperty(value = "支付中心用来支付的id")
  private String idOut;

}
