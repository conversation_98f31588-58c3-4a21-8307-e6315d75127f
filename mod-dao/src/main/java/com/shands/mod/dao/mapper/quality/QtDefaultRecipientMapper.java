package com.shands.mod.dao.mapper.quality;

import com.shands.mod.dao.model.quality.bo.v2.UpdateRectificationTaskBo;
import com.shands.mod.dao.model.quality.po.QtDefaultRecipient;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 部门整改任务默认接收人表(QtDefaultRecipient)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-04-15 14:41:24
 */
public interface QtDefaultRecipientMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    QtDefaultRecipient queryById(Integer id);

    /**
     * 统计总行数
     *
     * @param qtDefaultRecipient 查询条件
     * @return 总行数
     */
    long count(QtDefaultRecipient qtDefaultRecipient);

    /**
     * 新增数据
     *
     * @param qtDefaultRecipient 实例对象
     * @return 影响行数
     */
    int insert(QtDefaultRecipient qtDefaultRecipient);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<QtDefaultRecipient> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<QtDefaultRecipient> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<QtDefaultRecipient> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<QtDefaultRecipient> entities);

    /**
     * 修改数据
     *
     * @param qtDefaultRecipient 实例对象
     * @return 影响行数
     */
    int update(QtDefaultRecipient qtDefaultRecipient);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

    QtDefaultRecipient queryDeptIdAndHotelId(@Param("updateRectificationTaskBo") UpdateRectificationTaskBo updateRectificationTaskBo);



}
