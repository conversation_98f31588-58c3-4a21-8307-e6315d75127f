package com.shands.mod.dao.model.enums;

import com.shands.mod.dao.model.res.gwincentive.OwnershipRes;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 事务部名称枚举
 */
@Getter
@AllArgsConstructor
public enum AdsHotelStatusEnum {
  QUIT("QUIT","退出"),
  CAMP("CAMP","在营"),
  BUILD("BUILD","筹建"),
  WAIT("WAIT","筹开"),
  SIGN("SIGN","签约");

  private String code;
  //描述
  private String desc;


  public static String getValue(String key){
    AdsHotelStatusEnum[] values = AdsHotelStatusEnum.values();
    for (AdsHotelStatusEnum value : values){
      if (value.code.equals(key)){
        return value.desc;
      }
    }
    return "";
  }

  public static List<OwnershipRes> getOwnershipList() {
    List<OwnershipRes> ownershipResList = new ArrayList<>();
    for (AdsHotelStatusEnum e : AdsHotelStatusEnum.values()) {
      OwnershipRes ownershipRes=new OwnershipRes();
      ownershipRes.setCode(e.code);
      ownershipRes.setName(e.desc);
      ownershipResList.add(ownershipRes);
    }
    return ownershipResList;
  }
}
