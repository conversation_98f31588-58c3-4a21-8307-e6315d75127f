package com.shands.mod.dao.model.req.fw;

import com.shands.mod.dao.model.req.PageModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/5/14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
public class ReservationActivityListReq extends PageModel {
  /** 方案名 */
  @ApiModelProperty(value = "方案名")
  private String name;
  /** 分类id */
  @ApiModelProperty(value = "分类id")
  private Integer classId;
  /** 活动状态 */
  @ApiModelProperty(value = "活动状态 1.上架 2.下架")
  private Integer status;

  private Integer companyId;
}
