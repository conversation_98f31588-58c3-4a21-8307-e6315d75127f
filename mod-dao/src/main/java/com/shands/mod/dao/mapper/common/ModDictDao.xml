<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.common.ModDictDao">

    <resultMap id="BaseResultMap" type="com.shands.mod.dao.model.common.ModDict">
        <!--@Table mod_dict-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="value" column="value" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="deleted" column="deleted" jdbcType="OTHER"/>
        <result property="status" column="status" jdbcType="OTHER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createUser" column="create_user" jdbcType="INTEGER"/>
        <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
          id, code, name, value,sort, deleted, status, remark, create_time, update_time, create_user, update_user
        from  mod_dict
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
          id, code, name, value,sort, deleted, status, remark, create_time, update_time, create_user, update_user
        from  mod_dict
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select
          id, code, name, value,sort, deleted, status, remark, create_time, update_time, create_user, update_user
        from  mod_dict
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="code != null and code != ''">
                and code = #{code}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="value != null and value != ''">
                and value = #{value}
            </if>
            <if test="sort != null">
              and sort = #{sort}
            </if>
            <if test="deleted != null">
                and deleted = #{deleted}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="createUser != null">
                and create_user = #{createUser}
            </if>
            <if test="updateUser != null">
                and update_user = #{updateUser}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into  mod_dict(code, name, value,sort, deleted, status, remark, create_time, update_time, create_user, update_user)
        values (#{code}, #{name}, #{value},#{sort}, #{deleted}, #{status}, #{remark}, #{createTime}, #{updateTime}, #{createUser}, #{updateUser})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update  mod_dict
        <set>
            <if test="code != null and code != ''">
                code = #{code},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="value != null and value != ''">
                value = #{value},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="createUser != null">
                create_user = #{createUser},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from  mod_dict where id = #{id}
    </delete>

</mapper>