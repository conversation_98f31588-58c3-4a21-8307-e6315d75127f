package com.shands.mod.dao.mapper.quality;

import com.shands.mod.dao.model.quality.bo.AnalyticalBo;
import com.shands.mod.dao.model.quality.bo.HotelCheckListBo;
import com.shands.mod.dao.model.quality.po.QtHotelCheck;
import com.shands.mod.dao.model.quality.vo.AnalyticalVo;
import com.shands.mod.dao.model.quality.vo.CheckOutVo;
import com.shands.mod.dao.model.quality.vo.HotelCheckListVo;
import com.shands.mod.dao.model.quality.vo.HotelNameVo;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * (QtHotelCheck)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-16 15:42:14
 */
public interface QtHotelCheckMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    QtHotelCheck queryById(Integer id);

    /**
     * 统计总行数
     *
     * @param qtHotelCheck 查询条件
     * @return 总行数
     */
    long count(QtHotelCheck qtHotelCheck);

    /**
     * 新增数据
     *
     * @param qtHotelCheck 实例对象
     * @return 影响行数
     */
    int insert(QtHotelCheck qtHotelCheck);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<QtHotelCheck> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<QtHotelCheck> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<QtHotelCheck> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<QtHotelCheck> entities);

    /**
     * 修改数据
     *
     * @param qtHotelCheck 实例对象
     * @return 影响行数
     */
    int update(QtHotelCheck qtHotelCheck);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

    List<HotelCheckListVo> findHotelCheckList(@Param("hotelCheckListBo") HotelCheckListBo hotelCheckListBo);

    List<CheckOutVo> selectTitleList();

    List<HotelNameVo> selectHotelNameList(@Param("analyticalBo") AnalyticalBo analyticalBo);

    List<AnalyticalVo> selectAnalyticalVo(@Param("analyticalBo") AnalyticalBo analyticalBo);

    List<CheckOutVo> searchHotelCheck(@Param("hotelId") String hotelId);
}
