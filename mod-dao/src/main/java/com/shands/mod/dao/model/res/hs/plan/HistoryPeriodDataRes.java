package com.shands.mod.dao.model.res.hs.plan;

import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-11-19
 * @description APP历史周期数据
 */
@Data
public class HistoryPeriodDataRes {

  /**
   * 项目名称
   */
  private String programName;

  /**
   * 项目id
   */
  private Integer programId;

  /**
   * 历史数据列表
   */
  private List<HistoryDataForProgramRes> historyList;

  /**
   * 未完成数
   */
  private Integer unCompleted;

  /**
   * 已分配数
   */
  private Integer allocated;

  /**
   * 分类排序
   */
  private Integer classifySort;

  /**
   * 项目排序
   */
  private Integer sort;
}
